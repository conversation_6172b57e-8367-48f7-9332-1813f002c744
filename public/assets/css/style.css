/* ===== CSS RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Colors */
    --primary-color: #1a365d;
    --primary-light: #2d5a87;
    --primary-dark: #0f2a44;
    --secondary-color: #d4af37;
    --secondary-light: #e6c866;
    --secondary-dark: #b8941f;
    --accent-color: #e53e3e;
    --success-color: #38a169;
    --warning-color: #dd6b20;
    --info-color: #3182ce;
    
    /* Neutrals */
    --white: #ffffff;
    --gray-50: #f7fafc;
    --gray-100: #edf2f7;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e0;
    --gray-400: #a0aec0;
    --gray-500: #718096;
    --gray-600: #4a5568;
    --gray-700: #2d3748;
    --gray-800: #1a202c;
    --gray-900: #171923;
    --black: #000000;
    
    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-heading: 'Playfair Display', Georgia, serif;
    
    /* Font Sizes */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-5xl: 3rem;
    --text-6xl: 3.75rem;
    
    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    --space-24: 6rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Border Radius */
    --radius-sm: 0.125rem;
    --radius: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-full: 9999px;
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
    
    /* Z-index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-primary);
    font-size: var(--text-base);
    line-height: 1.6;
    color: var(--gray-700);
    background-color: var(--white);
    overflow-x: hidden;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 600;
    line-height: 1.2;
    color: var(--gray-900);
    margin-bottom: var(--space-4);
}

h1 { font-size: var(--text-5xl); }
h2 { font-size: var(--text-4xl); }
h3 { font-size: var(--text-3xl); }
h4 { font-size: var(--text-2xl); }
h5 { font-size: var(--text-xl); }
h6 { font-size: var(--text-lg); }

p {
    margin-bottom: var(--space-4);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-light);
}

/* ===== UTILITY CLASSES ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

.container-fluid {
    width: 100%;
    padding: 0 var(--space-4);
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.hidden { display: none !important; }
.visible { display: block !important; }

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* ===== BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-3) var(--space-6);
    font-size: var(--text-base);
    font-weight: 500;
    line-height: 1;
    border: 1px solid transparent;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    white-space: nowrap;
}

.btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    color: var(--white);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: var(--white);
    border-color: var(--secondary-color);
}

.btn-secondary:hover {
    background-color: var(--secondary-dark);
    border-color: var(--secondary-dark);
    color: var(--white);
}

.btn-outline {
    background-color: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

.btn-lg {
    padding: var(--space-4) var(--space-8);
    font-size: var(--text-lg);
}

.btn-sm {
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-sm);
}

/* ===== LOADING SPINNER ===== */
.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 50vh;
    gap: var(--space-4);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--gray-200);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== NAVIGATION ===== */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--gray-200);
    z-index: var(--z-fixed);
    transition: all var(--transition-normal);
}

.navbar.scrolled {
    background-color: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-md);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--space-4);
    height: 80px;
}

.nav-brand {
    display: flex;
    align-items: center;
}

.brand-link {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    color: var(--gray-900);
    font-weight: 600;
    font-size: var(--text-xl);
}

.brand-logo {
    height: 40px;
    width: auto;
}

.nav-menu {
    display: flex;
    align-items: center;
}

.nav-list {
    display: flex;
    align-items: center;
    gap: var(--space-8);
    list-style: none;
    margin: 0;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--space-1);
    padding: var(--space-2) var(--space-3);
    color: var(--gray-700);
    font-weight: 500;
    border-radius: var(--radius);
    transition: all var(--transition-fast);
}

.nav-link:hover {
    color: var(--primary-color);
    background-color: var(--gray-50);
}

.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    min-width: 200px;
    padding: var(--space-2);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-fast);
    z-index: var(--z-dropdown);
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    list-style: none;
}

.dropdown-menu a {
    display: block;
    padding: var(--space-2) var(--space-3);
    color: var(--gray-700);
    border-radius: var(--radius);
    transition: all var(--transition-fast);
}

.dropdown-menu a:hover {
    background-color: var(--gray-50);
    color: var(--primary-color);
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.language-switcher {
    position: relative;
}

.lang-btn {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-3);
    background: none;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius);
    color: var(--gray-700);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.lang-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.lang-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    min-width: 150px;
    padding: var(--space-2);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-fast);
    z-index: var(--z-dropdown);
    list-style: none;
}

.language-switcher:hover .lang-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.lang-menu a {
    display: block;
    padding: var(--space-2) var(--space-3);
    color: var(--gray-700);
    border-radius: var(--radius);
    transition: all var(--transition-fast);
}

.lang-menu a:hover {
    background-color: var(--gray-50);
    color: var(--primary-color);
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--space-2);
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background-color: var(--gray-700);
    border-radius: var(--radius-full);
    transition: all var(--transition-fast);
}

/* ===== MAIN CONTENT ===== */
.main-content {
    margin-top: 80px;
    min-height: calc(100vh - 80px);
}

/* ===== FOOTER ===== */
.footer {
    background-color: var(--gray-900);
    color: var(--gray-300);
    padding: var(--space-16) 0 var(--space-8);
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-4);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-8);
}

.footer-section h4 {
    color: var(--white);
    margin-bottom: var(--space-4);
    font-size: var(--text-lg);
}

.footer-brand p {
    color: var(--gray-400);
    margin-bottom: var(--space-6);
}

.footer-logo {
    height: 40px;
    margin-bottom: var(--space-4);
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: var(--space-2);
}

.footer-links a {
    color: var(--gray-400);
    transition: color var(--transition-fast);
}

.footer-links a:hover {
    color: var(--white);
}

.contact-info p {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin-bottom: var(--space-2);
    color: var(--gray-400);
}

.social-links {
    display: flex;
    gap: var(--space-3);
    margin-top: var(--space-4);
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: var(--gray-800);
    color: var(--gray-400);
    border-radius: var(--radius-full);
    transition: all var(--transition-fast);
}

.social-links a:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

.footer-bottom {
    border-top: 1px solid var(--gray-800);
    margin-top: var(--space-8);
    padding-top: var(--space-6);
}

.footer-bottom-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-4);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--space-4);
}

.footer-legal {
    display: flex;
    gap: var(--space-6);
}

.footer-legal a {
    color: var(--gray-400);
    font-size: var(--text-sm);
}

.footer-legal a:hover {
    color: var(--white);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    :root {
        --text-5xl: 2.25rem;
        --text-4xl: 1.875rem;
        --text-3xl: 1.5rem;
    }

    .container {
        padding: 0 var(--space-3);
    }

    h1 { font-size: var(--text-4xl); }
    h2 { font-size: var(--text-3xl); }
    h3 { font-size: var(--text-2xl); }

    /* Navigation Mobile */
    .nav-container {
        padding: var(--space-3);
        height: 70px;
    }

    .main-content {
        margin-top: 70px;
    }

    .nav-menu {
        position: fixed;
        top: 70px;
        left: 0;
        right: 0;
        background-color: var(--white);
        border-top: 1px solid var(--gray-200);
        box-shadow: var(--shadow-lg);
        padding: var(--space-4);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-normal);
    }

    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-list {
        flex-direction: column;
        gap: var(--space-4);
        align-items: stretch;
    }

    .nav-link {
        padding: var(--space-3);
        border-radius: var(--radius-lg);
    }

    .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        border: none;
        background-color: var(--gray-50);
        margin-top: var(--space-2);
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .nav-actions {
        gap: var(--space-2);
    }

    .cta-btn {
        display: none;
    }

    /* Footer Mobile */
    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--space-6);
    }

    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--space-2);
    }

    .btn {
        padding: var(--space-3) var(--space-4);
        font-size: var(--text-sm);
    }

    .btn-lg {
        padding: var(--space-3) var(--space-6);
        font-size: var(--text-base);
    }

    .nav-container {
        padding: var(--space-2);
    }

    .brand-link {
        font-size: var(--text-lg);
    }

    .brand-logo {
        height: 32px;
    }

    .footer {
        padding: var(--space-12) 0 var(--space-6);
    }
}
