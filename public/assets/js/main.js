// ===== MAIN APPLICATION ENTRY POINT =====
import { Router } from './Router.js';

class App {
    constructor() {
        this.router = null;
        this.currentLang = 'fr';
        this.translations = {};
        this.init();
    }

    async init() {
        try {
            // Initialize router
            this.router = new Router();
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Initialize language
            await this.initializeLanguage();
            
            // Initialize navigation
            this.initializeNavigation();
            
            // Start router
            this.router.init();
            
            console.log('App initialized successfully');
        } catch (error) {
            console.error('Error initializing app:', error);
        }
    }

    setupEventListeners() {
        // Language switcher
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-lang]')) {
                e.preventDefault();
                const lang = e.target.getAttribute('data-lang');
                this.changeLanguage(lang);
            }
        });

        // Navigation links
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-route]') || e.target.closest('[data-route]')) {
                e.preventDefault();
                const element = e.target.matches('[data-route]') ? e.target : e.target.closest('[data-route]');
                const route = element.getAttribute('data-route');
                this.router.navigate(route);
            }
        });

        // Mobile menu toggle
        const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
        const navMenu = document.getElementById('nav-menu');
        
        if (mobileMenuToggle && navMenu) {
            mobileMenuToggle.addEventListener('click', () => {
                navMenu.classList.toggle('active');
                this.toggleMobileMenuIcon(mobileMenuToggle, navMenu.classList.contains('active'));
            });
        }

        // Close mobile menu when clicking outside
        document.addEventListener('click', (e) => {
            if (navMenu && !navMenu.contains(e.target) && !mobileMenuToggle.contains(e.target)) {
                navMenu.classList.remove('active');
                this.toggleMobileMenuIcon(mobileMenuToggle, false);
            }
        });

        // Navbar scroll effect
        window.addEventListener('scroll', () => {
            const navbar = document.getElementById('main-nav');
            if (navbar) {
                if (window.scrollY > 50) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }
            }
        });

        // Language menu toggle
        const langBtn = document.getElementById('lang-btn');
        const langMenu = document.getElementById('lang-menu');
        
        if (langBtn && langMenu) {
            langBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                langMenu.classList.toggle('active');
            });

            document.addEventListener('click', () => {
                langMenu.classList.remove('active');
            });
        }
    }

    toggleMobileMenuIcon(button, isOpen) {
        const spans = button.querySelectorAll('span');
        if (isOpen) {
            spans[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
            spans[1].style.opacity = '0';
            spans[2].style.transform = 'rotate(-45deg) translate(7px, -6px)';
        } else {
            spans[0].style.transform = 'none';
            spans[1].style.opacity = '1';
            spans[2].style.transform = 'none';
        }
    }

    async initializeLanguage() {
        // Get language from URL or localStorage
        const urlParams = new URLSearchParams(window.location.search);
        const urlLang = urlParams.get('lang');
        const storedLang = localStorage.getItem('preferred-language');
        
        this.currentLang = urlLang || storedLang || 'fr';
        
        // Load translations
        await this.loadTranslations(this.currentLang);
        
        // Update UI
        this.updateLanguageUI();
    }

    async loadTranslations(lang) {
        try {
            const response = await fetch(`/backend/lang/index.php?lang=${lang}&page=common`);
            if (response.ok) {
                this.translations = await response.json();
                this.applyTranslations();
            }
        } catch (error) {
            console.error('Error loading translations:', error);
        }
    }

    applyTranslations() {
        document.querySelectorAll('[data-i18n]').forEach(element => {
            const key = element.getAttribute('data-i18n');
            const translation = this.getTranslation(key);
            if (translation) {
                element.textContent = translation;
            }
        });
    }

    getTranslation(key) {
        const keys = key.split('.');
        let value = this.translations;
        
        for (const k of keys) {
            if (value && typeof value === 'object' && k in value) {
                value = value[k];
            } else {
                return null;
            }
        }
        
        return typeof value === 'string' ? value : null;
    }

    async changeLanguage(lang) {
        if (lang === this.currentLang) return;
        
        this.currentLang = lang;
        localStorage.setItem('preferred-language', lang);
        
        // Update URL
        const url = new URL(window.location);
        url.searchParams.set('lang', lang);
        window.history.replaceState({}, '', url);
        
        // Load new translations
        await this.loadTranslations(lang);
        
        // Update UI
        this.updateLanguageUI();
        
        // Reload current page content
        if (this.router) {
            this.router.loadCurrentRoute();
        }
    }

    updateLanguageUI() {
        const currentLangElement = document.getElementById('current-lang');
        if (currentLangElement) {
            currentLangElement.textContent = this.currentLang.toUpperCase();
        }
        
        // Update document language
        document.documentElement.lang = this.currentLang;
    }

    initializeNavigation() {
        // Set active navigation item based on current route
        this.updateActiveNavigation();
    }

    updateActiveNavigation() {
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.nav-link[data-route]');
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            const route = link.getAttribute('data-route');
            
            if ((currentPath === '/' && route === 'home') || 
                currentPath.includes(route)) {
                link.classList.add('active');
            }
        });
    }

    // Utility methods
    showLoading() {
        const loading = document.getElementById('loading');
        if (loading) {
            loading.style.display = 'flex';
        }
    }

    hideLoading() {
        const loading = document.getElementById('loading');
        if (loading) {
            loading.style.display = 'none';
        }
    }

    showError(message) {
        // TODO: Implement error notification system
        console.error(message);
    }

    // Public API
    getCurrentLanguage() {
        return this.currentLang;
    }

    getRouter() {
        return this.router;
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new App();
});

// Export for module usage
export default App;
