// ===== HOME PAGE MODULE =====

class HomeModule {
    constructor() {
        this.data = null;
        this.lang = 'fr';
    }

    async init(context) {
        this.lang = context.lang;
        
        try {
            // Load page content from backend
            await this.loadContent();
            
            // Setup page-specific functionality
            this.setupEventListeners();
            
            console.log('Home module initialized');
        } catch (error) {
            console.error('Error initializing home module:', error);
        }
    }

    async loadContent() {
        try {
            const response = await fetch(`/backend/lang/index.php?lang=${this.lang}&page=home`);
            if (response.ok) {
                this.data = await response.json();
            } else {
                throw new Error('Failed to load home content');
            }
        } catch (error) {
            console.error('Error loading home content:', error);
            // Fallback to default content
            this.data = this.getDefaultContent();
        }
    }

    setupEventListeners() {
        // Setup event listeners after content is loaded
        setTimeout(() => {
            this.setupHeroSlider();
            this.setupCountryCards();
            this.setupTestimonials();
            this.setupCTAButtons();
            this.setupAnimations();
        }, 100);
    }

    setupHeroSlider() {
        const heroSlider = document.querySelector('.hero-slider');
        if (!heroSlider) return;

        const slides = heroSlider.querySelectorAll('.hero-slide');
        const indicators = heroSlider.querySelectorAll('.slider-indicator');
        let currentSlide = 0;

        const showSlide = (index) => {
            slides.forEach((slide, i) => {
                slide.classList.toggle('active', i === index);
            });
            indicators.forEach((indicator, i) => {
                indicator.classList.toggle('active', i === index);
            });
        };

        const nextSlide = () => {
            currentSlide = (currentSlide + 1) % slides.length;
            showSlide(currentSlide);
        };

        // Auto-advance slides
        setInterval(nextSlide, 5000);

        // Manual navigation
        indicators.forEach((indicator, index) => {
            indicator.addEventListener('click', () => {
                currentSlide = index;
                showSlide(currentSlide);
            });
        });
    }

    setupCountryCards() {
        const countryCards = document.querySelectorAll('.country-card');
        
        countryCards.forEach(card => {
            card.addEventListener('click', (e) => {
                e.preventDefault();
                const country = card.getAttribute('data-country');
                const type = card.getAttribute('data-type');
                
                if (country && type) {
                    window.app.getRouter().navigate(`${type}/${country}`);
                }
            });

            // Add hover effects
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-5px)';
            });

            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0)';
            });
        });
    }

    setupTestimonials() {
        const testimonialSlider = document.querySelector('.testimonials-slider');
        if (!testimonialSlider) return;

        const testimonials = testimonialSlider.querySelectorAll('.testimonial');
        let currentTestimonial = 0;

        const showTestimonial = (index) => {
            testimonials.forEach((testimonial, i) => {
                testimonial.classList.toggle('active', i === index);
            });
        };

        const nextTestimonial = () => {
            currentTestimonial = (currentTestimonial + 1) % testimonials.length;
            showTestimonial(currentTestimonial);
        };

        // Auto-advance testimonials
        setInterval(nextTestimonial, 7000);

        // Initialize first testimonial
        showTestimonial(0);
    }

    setupCTAButtons() {
        const ctaButtons = document.querySelectorAll('.cta-button');
        
        ctaButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const action = button.getAttribute('data-action');
                
                switch (action) {
                    case 'discover-programs':
                        window.app.getRouter().navigate('citizenship');
                        break;
                    case 'contact':
                        window.app.getRouter().navigate('contact');
                        break;
                    case 'consultation':
                        this.openConsultationModal();
                        break;
                    default:
                        console.log('Unknown CTA action:', action);
                }
            });
        });
    }

    setupAnimations() {
        // Intersection Observer for scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);

        // Observe elements with animation classes
        document.querySelectorAll('.animate-on-scroll').forEach(el => {
            observer.observe(el);
        });

        // Counter animations
        this.setupCounterAnimations();
    }

    setupCounterAnimations() {
        const counters = document.querySelectorAll('.counter');
        
        counters.forEach(counter => {
            const target = parseInt(counter.getAttribute('data-target'));
            const duration = 2000; // 2 seconds
            const increment = target / (duration / 16); // 60fps
            let current = 0;

            const updateCounter = () => {
                current += increment;
                if (current < target) {
                    counter.textContent = Math.floor(current);
                    requestAnimationFrame(updateCounter);
                } else {
                    counter.textContent = target;
                }
            };

            // Start animation when element is visible
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        updateCounter();
                        observer.unobserve(entry.target);
                    }
                });
            });

            observer.observe(counter);
        });
    }

    openConsultationModal() {
        // TODO: Implement consultation modal
        console.log('Opening consultation modal...');
        window.app.getRouter().navigate('contact');
    }

    getDefaultContent() {
        return {
            hero: {
                title: "Investissez dans votre avenir",
                subtitle: "Accédez à la liberté mondiale",
                description: "Découvrez nos programmes de citoyenneté et résidence par investissement",
                cta: "Découvrez nos programmes"
            },
            stats: {
                countries: 25,
                clients: 1000,
                success_rate: 98,
                experience: 15
            },
            featured_countries: [
                {
                    name: "Portugal",
                    type: "residency",
                    investment: "€280,000",
                    processing_time: "6-8 mois"
                },
                {
                    name: "Malta",
                    type: "citizenship",
                    investment: "€690,000",
                    processing_time: "12-14 mois"
                },
                {
                    name: "Greece",
                    type: "residency",
                    investment: "€250,000",
                    processing_time: "2-3 mois"
                }
            ]
        };
    }

    // Public methods
    getData() {
        return this.data;
    }

    refresh() {
        this.loadContent();
    }
}

export default new HomeModule();
