// ===== THANK YOU PAGE MODULE =====

class ThankYouModule {
    constructor() {
        this.data = null;
        this.lang = 'fr';
        this.params = {};
    }

    async init(context) {
        this.lang = context.lang;
        this.params = context.params || {};
        
        try {
            // Load page content from backend
            await this.loadContent();
            
            // Setup page-specific functionality
            this.setupEventListeners();
            
            // Track conversion if needed
            this.trackConversion();
            
            console.log('Thank you module initialized');
        } catch (error) {
            console.error('Error initializing thank you module:', error);
        }
    }

    async loadContent() {
        try {
            const response = await fetch(`/backend/lang/index.php?lang=${this.lang}&page=thank-you`);
            if (response.ok) {
                this.data = await response.json();
            } else {
                // Use default content if API fails
                this.data = this.getDefaultContent();
            }
        } catch (error) {
            console.error('Error loading thank you content:', error);
            // Fallback to default content
            this.data = this.getDefaultContent();
        }
    }

    setupEventListeners() {
        // Setup event listeners after content is loaded
        setTimeout(() => {
            this.setupCTAButtons();
            this.setupSocialSharing();
            this.setupRedirectTimer();
        }, 100);
    }

    setupCTAButtons() {
        const ctaButtons = document.querySelectorAll('.cta-button');
        
        ctaButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const action = button.getAttribute('data-action');
                
                switch (action) {
                    case 'home':
                        window.app.getRouter().navigate('home');
                        break;
                    case 'programs':
                        window.app.getRouter().navigate('citizenship');
                        break;
                    case 'contact':
                        window.app.getRouter().navigate('contact');
                        break;
                    case 'phone':
                        window.open('tel:+33123456789');
                        break;
                    case 'whatsapp':
                        window.open('https://wa.me/33123456789', '_blank');
                        break;
                    case 'calendar':
                        this.openCalendarBooking();
                        break;
                    default:
                        console.log('Unknown CTA action:', action);
                }
            });
        });
    }

    setupSocialSharing() {
        const shareButtons = document.querySelectorAll('.share-button');
        
        shareButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const platform = button.getAttribute('data-platform');
                this.shareOnSocial(platform);
            });
        });
    }

    setupRedirectTimer() {
        const redirectTimer = document.getElementById('redirect-timer');
        if (!redirectTimer) return;

        let countdown = 10; // 10 seconds
        const updateTimer = () => {
            redirectTimer.textContent = countdown;
            countdown--;

            if (countdown < 0) {
                // Redirect to home page
                window.app.getRouter().navigate('home');
            } else {
                setTimeout(updateTimer, 1000);
            }
        };

        // Start countdown after 3 seconds
        setTimeout(updateTimer, 3000);
    }

    trackConversion() {
        // Track form submission conversion
        if (typeof gtag !== 'undefined') {
            gtag('event', 'conversion', {
                'send_to': 'AW-CONVERSION_ID/CONVERSION_LABEL',
                'value': 1.0,
                'currency': 'EUR'
            });
        }

        // Track with Facebook Pixel
        if (typeof fbq !== 'undefined') {
            fbq('track', 'Lead');
        }

        // Track with custom analytics
        this.trackCustomEvent('form_submission', {
            type: this.params.type || 'contact',
            page: 'thank-you',
            timestamp: new Date().toISOString()
        });
    }

    trackCustomEvent(eventName, data) {
        // Send to your analytics endpoint
        fetch('/backend/analytics/track.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                event: eventName,
                data: data,
                url: window.location.href,
                referrer: document.referrer,
                timestamp: new Date().toISOString()
            })
        }).catch(error => {
            console.error('Analytics tracking failed:', error);
        });
    }

    shareOnSocial(platform) {
        const url = encodeURIComponent(window.location.origin);
        const text = encodeURIComponent('Je viens de découvrir CitizenshipPro pour les programmes de citoyenneté par investissement !');
        
        let shareUrl = '';
        
        switch (platform) {
            case 'facebook':
                shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}`;
                break;
            case 'twitter':
                shareUrl = `https://twitter.com/intent/tweet?url=${url}&text=${text}`;
                break;
            case 'linkedin':
                shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${url}`;
                break;
            case 'whatsapp':
                shareUrl = `https://wa.me/?text=${text}%20${url}`;
                break;
            default:
                console.log('Unknown social platform:', platform);
                return;
        }
        
        if (shareUrl) {
            window.open(shareUrl, '_blank', 'width=600,height=400');
        }
    }

    openCalendarBooking() {
        // Open calendar booking system (e.g., Calendly)
        const calendlyUrl = 'https://calendly.com/citizenshippro/consultation';
        window.open(calendlyUrl, '_blank');
    }

    getPersonalizedMessage() {
        const name = this.params.name || '';
        const type = this.params.type || 'contact';
        
        if (name) {
            if (this.lang === 'en') {
                return `Thank you ${name} for your interest in our services!`;
            } else {
                return `Merci ${name} pour votre intérêt pour nos services !`;
            }
        } else {
            if (this.lang === 'en') {
                return 'Thank you for your interest in our services!';
            } else {
                return 'Merci pour votre intérêt pour nos services !';
            }
        }
    }

    getDefaultContent() {
        if (this.lang === 'en') {
            return {
                hero: {
                    title: 'Thank you!',
                    subtitle: 'Your message has been sent successfully',
                    description: 'One of our experts will contact you within 24 hours to discuss your project.'
                },
                next_steps: {
                    title: 'What happens next?',
                    steps: [
                        {
                            icon: 'fas fa-envelope-open',
                            title: 'Confirmation email',
                            description: 'You will receive a confirmation email shortly'
                        },
                        {
                            icon: 'fas fa-phone',
                            title: 'Expert contact',
                            description: 'Our expert will call you within 24 hours'
                        },
                        {
                            icon: 'fas fa-calendar',
                            title: 'Consultation',
                            description: 'We will schedule a detailed consultation'
                        }
                    ]
                },
                cta: {
                    title: 'Need immediate assistance?',
                    description: 'Our team is available to answer your questions',
                    buttons: [
                        { text: 'Call now', action: 'phone' },
                        { text: 'WhatsApp', action: 'whatsapp' }
                    ]
                }
            };
        } else {
            return {
                hero: {
                    title: 'Merci !',
                    subtitle: 'Votre message a été envoyé avec succès',
                    description: 'Un de nos experts vous contactera dans les 24 heures pour discuter de votre projet.'
                },
                next_steps: {
                    title: 'Que se passe-t-il maintenant ?',
                    steps: [
                        {
                            icon: 'fas fa-envelope-open',
                            title: 'Email de confirmation',
                            description: 'Vous recevrez un email de confirmation sous peu'
                        },
                        {
                            icon: 'fas fa-phone',
                            title: 'Contact expert',
                            description: 'Notre expert vous appellera dans les 24 heures'
                        },
                        {
                            icon: 'fas fa-calendar',
                            title: 'Consultation',
                            description: 'Nous planifierons une consultation détaillée'
                        }
                    ]
                },
                cta: {
                    title: 'Besoin d\'une assistance immédiate ?',
                    description: 'Notre équipe est disponible pour répondre à vos questions',
                    buttons: [
                        { text: 'Appeler maintenant', action: 'phone' },
                        { text: 'WhatsApp', action: 'whatsapp' }
                    ]
                }
            };
        }
    }

    // Public methods
    getData() {
        return this.data;
    }

    getParams() {
        return this.params;
    }

    refresh() {
        this.loadContent();
    }
}

export default new ThankYouModule();
