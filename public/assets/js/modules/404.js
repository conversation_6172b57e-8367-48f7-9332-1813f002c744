// ===== 404 PAGE MODULE =====

class NotFoundModule {
    constructor() {
        this.data = null;
        this.lang = 'fr';
    }

    async init(context) {
        this.lang = context.lang;
        
        try {
            // Load page content
            this.data = this.getDefaultContent();
            
            // Setup page-specific functionality
            this.setupEventListeners();
            
            // Track 404 error
            this.track404Error();
            
            console.log('404 module initialized');
        } catch (error) {
            console.error('Error initializing 404 module:', error);
        }
    }

    setupEventListeners() {
        setTimeout(() => {
            this.setupNavigationButtons();
            this.setupSearchFunctionality();
        }, 100);
    }

    setupNavigationButtons() {
        const navButtons = document.querySelectorAll('.nav-button');
        
        navButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const route = button.getAttribute('data-route');
                
                if (route) {
                    window.app.getRouter().navigate(route);
                }
            });
        });
    }

    setupSearchFunctionality() {
        const searchForm = document.getElementById('search-form');
        const searchInput = document.getElementById('search-input');
        
        if (searchForm && searchInput) {
            searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                const query = searchInput.value.trim();
                
                if (query) {
                    this.performSearch(query);
                }
            });
        }
    }

    performSearch(query) {
        // Simple search logic - redirect to relevant pages based on keywords
        const searchTerms = query.toLowerCase();
        
        if (searchTerms.includes('citoyenneté') || searchTerms.includes('citizenship') || searchTerms.includes('passeport') || searchTerms.includes('passport')) {
            window.app.getRouter().navigate('citizenship');
        } else if (searchTerms.includes('résidence') || searchTerms.includes('residency') || searchTerms.includes('visa')) {
            window.app.getRouter().navigate('residency');
        } else if (searchTerms.includes('contact') || searchTerms.includes('aide') || searchTerms.includes('help')) {
            window.app.getRouter().navigate('contact');
        } else if (searchTerms.includes('service')) {
            window.app.getRouter().navigate('services');
        } else if (searchTerms.includes('blog') || searchTerms.includes('article') || searchTerms.includes('actualité')) {
            window.app.getRouter().navigate('blog');
        } else {
            // Default to home page
            window.app.getRouter().navigate('home');
        }
    }

    track404Error() {
        // Track 404 error for analytics
        const errorData = {
            page: '404',
            url: window.location.href,
            referrer: document.referrer,
            timestamp: new Date().toISOString(),
            user_agent: navigator.userAgent
        };

        // Send to analytics
        if (typeof gtag !== 'undefined') {
            gtag('event', 'page_not_found', {
                'page_location': window.location.href,
                'page_referrer': document.referrer
            });
        }

        // Custom analytics tracking
        fetch('/backend/analytics/track.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                event: '404_error',
                data: errorData
            })
        }).catch(error => {
            console.error('Analytics tracking failed:', error);
        });
    }

    getDefaultContent() {
        if (this.lang === 'en') {
            return {
                hero: {
                    title: 'Page Not Found',
                    subtitle: 'The page you are looking for does not exist',
                    description: 'The page may have been moved, deleted, or you may have entered an incorrect URL.'
                },
                suggestions: {
                    title: 'What can you do?',
                    items: [
                        {
                            icon: 'fas fa-home',
                            title: 'Go to Homepage',
                            description: 'Return to our homepage to find what you need',
                            route: 'home'
                        },
                        {
                            icon: 'fas fa-passport',
                            title: 'Citizenship Programs',
                            description: 'Explore our citizenship by investment programs',
                            route: 'citizenship'
                        },
                        {
                            icon: 'fas fa-map-marker-alt',
                            title: 'Residency Programs',
                            description: 'Discover our residency by investment options',
                            route: 'residency'
                        },
                        {
                            icon: 'fas fa-phone',
                            title: 'Contact Us',
                            description: 'Get in touch with our experts',
                            route: 'contact'
                        }
                    ]
                },
                search: {
                    title: 'Search our site',
                    placeholder: 'What are you looking for?',
                    button: 'Search'
                }
            };
        } else {
            return {
                hero: {
                    title: 'Page non trouvée',
                    subtitle: 'La page que vous recherchez n\'existe pas',
                    description: 'La page a peut-être été déplacée, supprimée, ou vous avez saisi une URL incorrecte.'
                },
                suggestions: {
                    title: 'Que pouvez-vous faire ?',
                    items: [
                        {
                            icon: 'fas fa-home',
                            title: 'Aller à l\'accueil',
                            description: 'Retournez à notre page d\'accueil pour trouver ce dont vous avez besoin',
                            route: 'home'
                        },
                        {
                            icon: 'fas fa-passport',
                            title: 'Programmes de Citoyenneté',
                            description: 'Explorez nos programmes de citoyenneté par investissement',
                            route: 'citizenship'
                        },
                        {
                            icon: 'fas fa-map-marker-alt',
                            title: 'Programmes de Résidence',
                            description: 'Découvrez nos options de résidence par investissement',
                            route: 'residency'
                        },
                        {
                            icon: 'fas fa-phone',
                            title: 'Nous contacter',
                            description: 'Entrez en contact avec nos experts',
                            route: 'contact'
                        }
                    ]
                },
                search: {
                    title: 'Rechercher sur notre site',
                    placeholder: 'Que recherchez-vous ?',
                    button: 'Rechercher'
                }
            };
        }
    }

    // Public methods
    getData() {
        return this.data;
    }

    refresh() {
        // No need to refresh for 404 page
    }
}

export default new NotFoundModule();
