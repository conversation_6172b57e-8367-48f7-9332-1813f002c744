// ===== CONTACT PAGE MODULE =====

class ContactModule {
    constructor() {
        this.data = null;
        this.lang = 'fr';
        this.form = null;
    }

    async init(context) {
        this.lang = context.lang;
        
        try {
            // Load page content from backend
            await this.loadContent();
            
            // Setup page-specific functionality
            this.setupEventListeners();
            
            console.log('Contact module initialized');
        } catch (error) {
            console.error('Error initializing contact module:', error);
        }
    }

    async loadContent() {
        try {
            const response = await fetch(`/backend/lang/index.php?lang=${this.lang}&page=contact`);
            if (response.ok) {
                this.data = await response.json();
            } else {
                throw new Error('Failed to load contact content');
            }
        } catch (error) {
            console.error('Error loading contact content:', error);
            // Fallback to default content
            this.data = this.getDefaultContent();
        }
    }

    setupEventListeners() {
        // Setup event listeners after content is loaded
        setTimeout(() => {
            this.setupContactForm();
            this.setupMapInteraction();
            this.setupPhoneLinks();
        }, 100);
    }

    setupContactForm() {
        this.form = document.getElementById('contact-form');
        if (!this.form) return;

        // Form submission
        this.form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleFormSubmission();
        });

        // Real-time validation
        const inputs = this.form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('blur', () => {
                this.validateField(input);
            });

            input.addEventListener('input', () => {
                this.clearFieldError(input);
            });
        });

        // Country selection
        const countrySelect = this.form.querySelector('#country');
        if (countrySelect) {
            this.populateCountryOptions(countrySelect);
        }

        // Program selection
        const programSelect = this.form.querySelector('#program');
        if (programSelect) {
            this.populateProgramOptions(programSelect);
        }
    }

    async handleFormSubmission() {
        try {
            // Show loading state
            this.setFormLoading(true);

            // Validate form
            if (!this.validateForm()) {
                this.setFormLoading(false);
                return;
            }

            // Collect form data
            const formData = this.collectFormData();

            // Submit to backend
            const response = await fetch('/backend/mail/mailer.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            });

            const result = await response.json();

            if (result.success) {
                // Redirect to thank you page
                window.app.getRouter().navigate('thank-you', { 
                    type: 'contact',
                    name: formData.name 
                });
            } else {
                throw new Error(result.message || 'Form submission failed');
            }

        } catch (error) {
            console.error('Error submitting form:', error);
            this.showFormError('Une erreur est survenue. Veuillez réessayer.');
        } finally {
            this.setFormLoading(false);
        }
    }

    validateForm() {
        const inputs = this.form.querySelectorAll('input[required], textarea[required], select[required]');
        let isValid = true;

        inputs.forEach(input => {
            if (!this.validateField(input)) {
                isValid = false;
            }
        });

        return isValid;
    }

    validateField(field) {
        const value = field.value.trim();
        const fieldName = field.name;
        let isValid = true;
        let errorMessage = '';

        // Required field validation
        if (field.hasAttribute('required') && !value) {
            isValid = false;
            errorMessage = 'Ce champ est obligatoire';
        }

        // Email validation
        if (fieldName === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                isValid = false;
                errorMessage = 'Adresse email invalide';
            }
        }

        // Phone validation
        if (fieldName === 'phone' && value) {
            const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
            if (!phoneRegex.test(value)) {
                isValid = false;
                errorMessage = 'Numéro de téléphone invalide';
            }
        }

        // Name validation
        if (fieldName === 'name' && value) {
            if (value.length < 2) {
                isValid = false;
                errorMessage = 'Le nom doit contenir au moins 2 caractères';
            }
        }

        // Message validation
        if (fieldName === 'message' && value) {
            if (value.length < 10) {
                isValid = false;
                errorMessage = 'Le message doit contenir au moins 10 caractères';
            }
        }

        // Show/hide error
        if (isValid) {
            this.clearFieldError(field);
        } else {
            this.showFieldError(field, errorMessage);
        }

        return isValid;
    }

    showFieldError(field, message) {
        this.clearFieldError(field);
        
        field.classList.add('error');
        
        const errorElement = document.createElement('div');
        errorElement.className = 'field-error';
        errorElement.textContent = message;
        
        field.parentNode.appendChild(errorElement);
    }

    clearFieldError(field) {
        field.classList.remove('error');
        
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }
    }

    collectFormData() {
        const formData = new FormData(this.form);
        const data = {
            type: 'contact',
            lang: this.lang,
            timestamp: new Date().toISOString()
        };

        for (const [key, value] of formData.entries()) {
            data[key] = value;
        }

        return data;
    }

    setFormLoading(loading) {
        const submitButton = this.form.querySelector('button[type="submit"]');
        const spinner = this.form.querySelector('.form-spinner');

        if (loading) {
            submitButton.disabled = true;
            submitButton.textContent = 'Envoi en cours...';
            if (spinner) spinner.style.display = 'block';
        } else {
            submitButton.disabled = false;
            submitButton.textContent = this.data?.form?.submit || 'Envoyer le message';
            if (spinner) spinner.style.display = 'none';
        }
    }

    showFormError(message) {
        // Remove existing error
        const existingError = this.form.querySelector('.form-error');
        if (existingError) {
            existingError.remove();
        }

        // Create new error
        const errorElement = document.createElement('div');
        errorElement.className = 'form-error';
        errorElement.textContent = message;
        
        this.form.insertBefore(errorElement, this.form.firstChild);
    }

    populateCountryOptions(select) {
        const countries = [
            'France', 'United Kingdom', 'Germany', 'Italy', 'Spain', 'Portugal',
            'United States', 'Canada', 'Australia', 'New Zealand', 'Switzerland',
            'Belgium', 'Netherlands', 'Austria', 'Sweden', 'Norway', 'Denmark',
            'Russia', 'China', 'Japan', 'South Korea', 'Singapore', 'Hong Kong',
            'UAE', 'Saudi Arabia', 'Qatar', 'Kuwait', 'Other'
        ];

        countries.forEach(country => {
            const option = document.createElement('option');
            option.value = country;
            option.textContent = country;
            select.appendChild(option);
        });
    }

    populateProgramOptions(select) {
        const programs = [
            { value: 'citizenship-malta', text: 'Citoyenneté - Malta' },
            { value: 'citizenship-antigua', text: 'Citoyenneté - Antigua & Barbuda' },
            { value: 'citizenship-grenada', text: 'Citoyenneté - Grenada' },
            { value: 'residency-portugal', text: 'Résidence - Portugal' },
            { value: 'residency-greece', text: 'Résidence - Greece' },
            { value: 'residency-spain', text: 'Résidence - Spain' },
            { value: 'consultation', text: 'Consultation générale' },
            { value: 'other', text: 'Autre' }
        ];

        programs.forEach(program => {
            const option = document.createElement('option');
            option.value = program.value;
            option.textContent = program.text;
            select.appendChild(option);
        });
    }

    setupMapInteraction() {
        // TODO: Implement interactive map if needed
        const mapContainer = document.getElementById('office-map');
        if (mapContainer) {
            // Placeholder for map implementation
            console.log('Map container found, implement map here');
        }
    }

    setupPhoneLinks() {
        const phoneLinks = document.querySelectorAll('a[href^="tel:"]');
        phoneLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                // Track phone click for analytics
                console.log('Phone link clicked:', link.href);
            });
        });
    }

    getDefaultContent() {
        return {
            hero: {
                title: "Contactez-nous",
                subtitle: "Parlons de votre projet",
                description: "Nos experts sont à votre disposition pour répondre à toutes vos questions."
            },
            form: {
                name: "Nom complet",
                email: "Adresse email",
                phone: "Téléphone",
                country: "Pays de résidence",
                program: "Programme d'intérêt",
                message: "Votre message",
                submit: "Envoyer le message",
                required: "Champ obligatoire"
            },
            info: {
                phone: "+33 1 23 45 67 89",
                email: "<EMAIL>",
                address: "123 Avenue des Champs-Élysées, 75008 Paris, France",
                hours: "Lun-Ven: 9h00-18h00"
            }
        };
    }

    // Public methods
    getData() {
        return this.data;
    }

    refresh() {
        this.loadContent();
    }
}

export default new ContactModule();
