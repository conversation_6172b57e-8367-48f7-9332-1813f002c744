// ===== CLIENT-SIDE ROUTER WITH LANGUAGE SUPPORT =====

export class Router {
    constructor() {
        this.routes = new Map();
        this.currentRoute = null;
        this.defaultLang = 'fr';
        this.supportedLangs = ['fr', 'en'];
        this.setupRoutes();
    }

    setupRoutes() {
        // Define all routes with their corresponding modules
        this.routes.set('home', {
            module: () => import('./modules/home.js'),
            template: () => import('./templates/home-template.js'),
            title: 'Accueil'
        });

        this.routes.set('citizenship', {
            module: () => import('./modules/citizenship.js'),
            template: () => import('./templates/citizenship-template.js'),
            title: 'Programmes de Citoyenneté'
        });

        this.routes.set('citizenship/:country', {
            module: () => import('./modules/citizenship-country.js'),
            template: () => import('./templates/citizenship-country-template.js'),
            title: 'Citoyenneté'
        });

        this.routes.set('residency', {
            module: () => import('./modules/residency.js'),
            template: () => import('./templates/residency-template.js'),
            title: 'Programmes de Résidence'
        });

        this.routes.set('residency/:country', {
            module: () => import('./modules/residency-country.js'),
            template: () => import('./templates/residency-country-template.js'),
            title: 'Résidence'
        });

        this.routes.set('services', {
            module: () => import('./modules/services.js'),
            template: () => import('./templates/services-template.js'),
            title: 'Services'
        });

        this.routes.set('about', {
            module: () => import('./modules/about.js'),
            template: () => import('./templates/about-template.js'),
            title: 'À propos'
        });

        this.routes.set('blog', {
            module: () => import('./modules/blog.js'),
            template: () => import('./templates/blog-template.js'),
            title: 'Blog'
        });

        this.routes.set('blog/:slug', {
            module: () => import('./modules/blog-article.js'),
            template: () => import('./templates/blog-article-template.js'),
            title: 'Article'
        });

        this.routes.set('contact', {
            module: () => import('./modules/contact.js'),
            template: () => import('./templates/contact-template.js'),
            title: 'Contact'
        });

        this.routes.set('thank-you', {
            module: () => import('./modules/thank-you.js'),
            template: () => import('./templates/thank-you-template.js'),
            title: 'Merci'
        });

        this.routes.set('legal-notice', {
            module: () => import('./modules/legal-notice.js'),
            template: () => import('./templates/legal-notice-template.js'),
            title: 'Mentions légales'
        });

        this.routes.set('privacy', {
            module: () => import('./modules/privacy.js'),
            template: () => import('./templates/privacy-template.js'),
            title: 'Confidentialité'
        });

        this.routes.set('404', {
            module: () => import('./modules/404.js'),
            template: () => import('./templates/404-template.js'),
            title: 'Page non trouvée'
        });
    }

    init() {
        // Listen for browser navigation
        window.addEventListener('popstate', (e) => {
            this.handleRoute(window.location.pathname);
        });

        // Handle initial route
        this.handleRoute(window.location.pathname);
    }

    navigate(route, params = {}) {
        const lang = this.getCurrentLanguage();
        let url = `/${lang}/${route}`;
        
        // Handle home route
        if (route === 'home') {
            url = lang === this.defaultLang ? '/' : `/${lang}`;
        }
        
        // Add parameters to URL if provided
        if (Object.keys(params).length > 0) {
            const searchParams = new URLSearchParams(params);
            url += `?${searchParams.toString()}`;
        }

        // Update browser history
        window.history.pushState({ route, params }, '', url);
        
        // Load the route
        this.handleRoute(window.location.pathname);
    }

    async handleRoute(path) {
        try {
            window.app?.showLoading();
            
            const { lang, route, params } = this.parsePath(path);
            
            // Validate language
            if (!this.supportedLangs.includes(lang)) {
                this.navigate('404');
                return;
            }

            // Find matching route
            const routeConfig = this.findRoute(route);
            if (!routeConfig) {
                this.navigate('404');
                return;
            }

            // Load and render the route
            await this.loadRoute(routeConfig, { lang, route, params });
            
            // Update current route
            this.currentRoute = { lang, route, params };
            
            window.app?.hideLoading();
        } catch (error) {
            console.error('Error handling route:', error);
            window.app?.showError('Erreur lors du chargement de la page');
            this.navigate('404');
        }
    }

    parsePath(path) {
        // Remove leading/trailing slashes
        path = path.replace(/^\/+|\/+$/g, '');
        
        // Default to home if empty
        if (!path) {
            return {
                lang: this.defaultLang,
                route: 'home',
                params: {}
            };
        }

        const segments = path.split('/');
        let lang = this.defaultLang;
        let route = 'home';
        let params = {};

        // Check if first segment is a language
        if (this.supportedLangs.includes(segments[0])) {
            lang = segments.shift();
        }

        // Get route
        if (segments.length > 0) {
            route = segments.join('/');
        }

        return { lang, route, params };
    }

    findRoute(route) {
        // Exact match first
        if (this.routes.has(route)) {
            return this.routes.get(route);
        }

        // Try pattern matching for dynamic routes
        for (const [pattern, config] of this.routes) {
            if (this.matchRoute(pattern, route)) {
                return config;
            }
        }

        return null;
    }

    matchRoute(pattern, route) {
        // Convert pattern to regex
        const regexPattern = pattern
            .replace(/:[^/]+/g, '([^/]+)')
            .replace(/\//g, '\\/');
        
        const regex = new RegExp(`^${regexPattern}$`);
        return regex.test(route);
    }

    extractParams(pattern, route) {
        const patternParts = pattern.split('/');
        const routeParts = route.split('/');
        const params = {};

        for (let i = 0; i < patternParts.length; i++) {
            const patternPart = patternParts[i];
            if (patternPart.startsWith(':')) {
                const paramName = patternPart.slice(1);
                params[paramName] = routeParts[i];
            }
        }

        return params;
    }

    async loadRoute(routeConfig, context) {
        try {
            // Load module and template
            const [moduleExport, templateExport] = await Promise.all([
                routeConfig.module(),
                routeConfig.template()
            ]);

            // Get the main content container
            const mainContent = document.getElementById('main-content');
            if (!mainContent) {
                throw new Error('Main content container not found');
            }

            // Initialize module if it has an init method
            if (moduleExport.default && typeof moduleExport.default.init === 'function') {
                await moduleExport.default.init(context);
            }

            // Render template if it exists
            if (templateExport.default) {
                const template = new templateExport.default();
                const content = await template.render(context);
                mainContent.innerHTML = content;
            }

            // Update page title
            this.updatePageTitle(routeConfig.title, context.lang);

            // Update active navigation
            window.app?.updateActiveNavigation();

        } catch (error) {
            console.error('Error loading route:', error);
            throw error;
        }
    }

    updatePageTitle(title, lang) {
        const siteName = 'CitizenshipPro';
        document.title = `${title} - ${siteName}`;
    }

    getCurrentLanguage() {
        return window.app?.getCurrentLanguage() || this.defaultLang;
    }

    getCurrentRoute() {
        return this.currentRoute;
    }

    loadCurrentRoute() {
        this.handleRoute(window.location.pathname);
    }

    // Utility method to build URLs with language prefix
    buildUrl(route, lang = null) {
        const currentLang = lang || this.getCurrentLanguage();
        
        if (route === 'home') {
            return currentLang === this.defaultLang ? '/' : `/${currentLang}`;
        }
        
        return `/${currentLang}/${route}`;
    }
}

export default Router;
