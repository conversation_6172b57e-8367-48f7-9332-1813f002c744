// ===== CONTACT PAGE TEMPLATE USING LIT =====
import { html, css, LitElement } from 'lit';

class ContactTemplate extends LitElement {
    static styles = css`
        :host {
            display: block;
        }
        
        /* Hero Section */
        .hero {
            padding: var(--space-20) 0 var(--space-16);
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            text-align: center;
        }
        
        .hero-content {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 var(--space-4);
        }
        
        .hero-title {
            font-size: var(--text-5xl);
            margin-bottom: var(--space-4);
        }
        
        .hero-subtitle {
            font-size: var(--text-2xl);
            margin-bottom: var(--space-6);
            opacity: 0.9;
        }
        
        .hero-description {
            font-size: var(--text-lg);
            opacity: 0.8;
        }
        
        /* Contact Section */
        .contact-section {
            padding: var(--space-20) 0;
        }
        
        .contact-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--space-4);
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--space-16);
            align-items: start;
        }
        
        /* Contact Form */
        .contact-form {
            background: white;
            padding: var(--space-8);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
        }
        
        .form-title {
            font-size: var(--text-2xl);
            margin-bottom: var(--space-6);
            color: var(--gray-900);
        }
        
        .form-group {
            margin-bottom: var(--space-6);
        }
        
        .form-label {
            display: block;
            margin-bottom: var(--space-2);
            font-weight: 500;
            color: var(--gray-700);
        }
        
        .form-input,
        .form-select,
        .form-textarea {
            width: 100%;
            padding: var(--space-3);
            border: 2px solid var(--gray-300);
            border-radius: var(--radius-lg);
            font-size: var(--text-base);
            transition: border-color var(--transition-fast);
        }
        
        .form-input:focus,
        .form-select:focus,
        .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
        }
        
        .form-input.error,
        .form-select.error,
        .form-textarea.error {
            border-color: var(--accent-color);
        }
        
        .form-textarea {
            min-height: 120px;
            resize: vertical;
        }
        
        .field-error {
            color: var(--accent-color);
            font-size: var(--text-sm);
            margin-top: var(--space-1);
        }
        
        .form-error {
            background-color: #fee;
            color: var(--accent-color);
            padding: var(--space-3);
            border-radius: var(--radius);
            margin-bottom: var(--space-4);
            border: 1px solid var(--accent-color);
        }
        
        .form-submit {
            width: 100%;
            padding: var(--space-4);
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--radius-lg);
            font-size: var(--text-lg);
            font-weight: 600;
            cursor: pointer;
            transition: background-color var(--transition-fast);
        }
        
        .form-submit:hover:not(:disabled) {
            background-color: var(--primary-dark);
        }
        
        .form-submit:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .form-spinner {
            display: none;
            text-align: center;
            margin-top: var(--space-2);
        }
        
        .required {
            color: var(--accent-color);
        }
        
        /* Contact Info */
        .contact-info {
            padding: var(--space-8);
        }
        
        .info-title {
            font-size: var(--text-2xl);
            margin-bottom: var(--space-6);
            color: var(--gray-900);
        }
        
        .info-item {
            display: flex;
            align-items: flex-start;
            gap: var(--space-4);
            margin-bottom: var(--space-6);
            padding: var(--space-4);
            background-color: var(--gray-50);
            border-radius: var(--radius-lg);
        }
        
        .info-icon {
            width: 50px;
            height: 50px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--text-xl);
            flex-shrink: 0;
        }
        
        .info-content h4 {
            margin-bottom: var(--space-2);
            color: var(--gray-900);
        }
        
        .info-content p {
            color: var(--gray-600);
            margin: 0;
        }
        
        .info-content a {
            color: var(--primary-color);
            text-decoration: none;
        }
        
        .info-content a:hover {
            text-decoration: underline;
        }
        
        /* Office Hours */
        .office-hours {
            background-color: var(--primary-color);
            color: white;
            padding: var(--space-6);
            border-radius: var(--radius-xl);
            margin-top: var(--space-8);
        }
        
        .office-hours h3 {
            margin-bottom: var(--space-4);
            color: white;
        }
        
        .hours-list {
            list-style: none;
            padding: 0;
        }
        
        .hours-list li {
            display: flex;
            justify-content: space-between;
            padding: var(--space-2) 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .hours-list li:last-child {
            border-bottom: none;
        }
        
        /* CTA Section */
        .cta-section {
            background-color: var(--gray-50);
            padding: var(--space-16) 0;
            text-align: center;
        }
        
        .cta-content {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 var(--space-4);
        }
        
        .cta-title {
            font-size: var(--text-3xl);
            margin-bottom: var(--space-4);
        }
        
        .cta-buttons {
            display: flex;
            gap: var(--space-4);
            justify-content: center;
            flex-wrap: wrap;
            margin-top: var(--space-6);
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .hero-title {
                font-size: var(--text-4xl);
            }
            
            .hero-subtitle {
                font-size: var(--text-xl);
            }
            
            .contact-container {
                grid-template-columns: 1fr;
                gap: var(--space-8);
            }
            
            .contact-form,
            .contact-info {
                padding: var(--space-6);
            }
            
            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
        
        @media (max-width: 480px) {
            .contact-form,
            .contact-info {
                padding: var(--space-4);
            }
            
            .info-item {
                flex-direction: column;
                text-align: center;
            }
        }
    `;

    async render(context) {
        const contactModule = await import('../modules/contact.js');
        const data = contactModule.default.getData();
        
        return html`
            <!-- Hero Section -->
            <section class="hero">
                <div class="hero-content">
                    <h1 class="hero-title">${data?.hero?.title || 'Contactez-nous'}</h1>
                    <p class="hero-subtitle">${data?.hero?.subtitle || 'Parlons de votre projet'}</p>
                    <p class="hero-description">${data?.hero?.description || 'Nos experts sont à votre disposition pour répondre à toutes vos questions.'}</p>
                </div>
            </section>

            <!-- Contact Section -->
            <section class="contact-section">
                <div class="contact-container">
                    <!-- Contact Form -->
                    <div class="contact-form">
                        <h2 class="form-title">Envoyez-nous un message</h2>
                        <form id="contact-form">
                            <div class="form-group">
                                <label for="name" class="form-label">
                                    ${data?.form?.name || 'Nom complet'} <span class="required">*</span>
                                </label>
                                <input 
                                    type="text" 
                                    id="name" 
                                    name="name" 
                                    class="form-input" 
                                    required
                                    placeholder="Votre nom complet"
                                >
                            </div>

                            <div class="form-group">
                                <label for="email" class="form-label">
                                    ${data?.form?.email || 'Adresse email'} <span class="required">*</span>
                                </label>
                                <input 
                                    type="email" 
                                    id="email" 
                                    name="email" 
                                    class="form-input" 
                                    required
                                    placeholder="<EMAIL>"
                                >
                            </div>

                            <div class="form-group">
                                <label for="phone" class="form-label">
                                    ${data?.form?.phone || 'Téléphone'}
                                </label>
                                <input 
                                    type="tel" 
                                    id="phone" 
                                    name="phone" 
                                    class="form-input"
                                    placeholder="+33 1 23 45 67 89"
                                >
                            </div>

                            <div class="form-group">
                                <label for="country" class="form-label">
                                    ${data?.form?.country || 'Pays de résidence'}
                                </label>
                                <select id="country" name="country" class="form-select">
                                    <option value="">Sélectionnez votre pays</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="program" class="form-label">
                                    ${data?.form?.program || 'Programme d\'intérêt'}
                                </label>
                                <select id="program" name="program" class="form-select">
                                    <option value="">Sélectionnez un programme</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="message" class="form-label">
                                    ${data?.form?.message || 'Votre message'} <span class="required">*</span>
                                </label>
                                <textarea 
                                    id="message" 
                                    name="message" 
                                    class="form-textarea" 
                                    required
                                    placeholder="Décrivez votre projet et vos objectifs..."
                                ></textarea>
                            </div>

                            <button type="submit" class="form-submit">
                                ${data?.form?.submit || 'Envoyer le message'}
                            </button>

                            <div class="form-spinner">
                                <div class="spinner"></div>
                            </div>
                        </form>
                    </div>

                    <!-- Contact Info -->
                    <div class="contact-info">
                        <h2 class="info-title">Nos coordonnées</h2>
                        
                        ${this.renderContactInfo(data?.info)}
                        
                        <div class="office-hours">
                            <h3>Horaires d'ouverture</h3>
                            <ul class="hours-list">
                                <li><span>Lundi - Vendredi</span><span>9h00 - 18h00</span></li>
                                <li><span>Samedi</span><span>10h00 - 16h00</span></li>
                                <li><span>Dimanche</span><span>Fermé</span></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>

            <!-- CTA Section -->
            <section class="cta-section">
                <div class="cta-content">
                    <h2 class="cta-title">Besoin d'une réponse immédiate ?</h2>
                    <p>Nos conseillers sont disponibles pour répondre à vos questions par téléphone ou WhatsApp.</p>
                    <div class="cta-buttons">
                        <a href="tel:+33123456789" class="btn btn-primary btn-lg">
                            <i class="fas fa-phone"></i> Appeler maintenant
                        </a>
                        <a href="https://wa.me/33123456789" class="btn btn-secondary btn-lg" target="_blank">
                            <i class="fab fa-whatsapp"></i> WhatsApp
                        </a>
                    </div>
                </div>
            </section>
        `;
    }

    renderContactInfo(info) {
        const defaultInfo = {
            phone: '+33 1 23 45 67 89',
            email: '<EMAIL>',
            address: '123 Avenue des Champs-Élysées, 75008 Paris, France'
        };

        const contactInfo = info || defaultInfo;

        return html`
            <div class="info-item">
                <div class="info-icon">
                    <i class="fas fa-phone"></i>
                </div>
                <div class="info-content">
                    <h4>Téléphone</h4>
                    <p><a href="tel:${contactInfo.phone.replace(/\s/g, '')}">${contactInfo.phone}</a></p>
                </div>
            </div>

            <div class="info-item">
                <div class="info-icon">
                    <i class="fas fa-envelope"></i>
                </div>
                <div class="info-content">
                    <h4>Email</h4>
                    <p><a href="mailto:${contactInfo.email}">${contactInfo.email}</a></p>
                </div>
            </div>

            <div class="info-item">
                <div class="info-icon">
                    <i class="fas fa-map-marker-alt"></i>
                </div>
                <div class="info-content">
                    <h4>Adresse</h4>
                    <p>${contactInfo.address}</p>
                </div>
            </div>

            <div class="info-item">
                <div class="info-icon">
                    <i class="fab fa-whatsapp"></i>
                </div>
                <div class="info-content">
                    <h4>WhatsApp</h4>
                    <p><a href="https://wa.me/33123456789" target="_blank">Démarrer une conversation</a></p>
                </div>
            </div>
        `;
    }
}

export default ContactTemplate;
