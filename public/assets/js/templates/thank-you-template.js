// ===== THANK YOU PAGE TEMPLATE USING LIT =====
import { html, css, LitElement } from 'lit';

class ThankYouTemplate extends LitElement {
    static styles = css`
        :host {
            display: block;
        }
        
        /* Hero Section */
        .hero {
            padding: var(--space-20) 0;
            background: linear-gradient(135deg, var(--success-color) 0%, #2f855a 100%);
            color: white;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('/assets/img/success-pattern.svg') center/cover;
            opacity: 0.1;
        }
        
        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 800px;
            margin: 0 auto;
            padding: 0 var(--space-4);
        }
        
        .success-icon {
            width: 100px;
            height: 100px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--space-6);
            font-size: var(--text-5xl);
            animation: successPulse 2s ease-in-out infinite;
        }
        
        .hero-title {
            font-size: var(--text-6xl);
            margin-bottom: var(--space-4);
            animation: fadeInUp 0.8s ease-out;
        }
        
        .hero-subtitle {
            font-size: var(--text-2xl);
            margin-bottom: var(--space-6);
            opacity: 0.9;
            animation: fadeInUp 0.8s ease-out 0.2s both;
        }
        
        .hero-description {
            font-size: var(--text-lg);
            opacity: 0.8;
            animation: fadeInUp 0.8s ease-out 0.4s both;
        }
        
        /* Next Steps Section */
        .next-steps {
            padding: var(--space-20) 0;
            background-color: var(--gray-50);
        }
        
        .section-title {
            text-align: center;
            font-size: var(--text-4xl);
            margin-bottom: var(--space-16);
            color: var(--gray-900);
        }
        
        .steps-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--space-8);
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--space-4);
        }
        
        .step-item {
            background: white;
            padding: var(--space-8);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            text-align: center;
            transition: transform var(--transition-normal);
            animation: fadeInUp 0.8s ease-out;
        }
        
        .step-item:nth-child(2) {
            animation-delay: 0.2s;
        }
        
        .step-item:nth-child(3) {
            animation-delay: 0.4s;
        }
        
        .step-item:hover {
            transform: translateY(-5px);
        }
        
        .step-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--space-4);
            color: white;
            font-size: var(--text-3xl);
        }
        
        .step-title {
            font-size: var(--text-xl);
            font-weight: 600;
            margin-bottom: var(--space-3);
            color: var(--gray-900);
        }
        
        .step-description {
            color: var(--gray-600);
            line-height: 1.6;
        }
        
        /* CTA Section */
        .cta-section {
            padding: var(--space-20) 0;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            text-align: center;
        }
        
        .cta-content {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 var(--space-4);
        }
        
        .cta-title {
            font-size: var(--text-4xl);
            margin-bottom: var(--space-4);
        }
        
        .cta-description {
            font-size: var(--text-lg);
            margin-bottom: var(--space-8);
            opacity: 0.9;
        }
        
        .cta-buttons {
            display: flex;
            gap: var(--space-4);
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .cta-button {
            padding: var(--space-4) var(--space-8);
            border: 2px solid white;
            border-radius: var(--radius-lg);
            color: white;
            text-decoration: none;
            font-weight: 600;
            font-size: var(--text-lg);
            transition: all var(--transition-fast);
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }
        
        .cta-button:hover {
            background-color: white;
            color: var(--primary-color);
        }
        
        .cta-button.primary {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }
        
        .cta-button.primary:hover {
            background-color: var(--secondary-light);
            border-color: var(--secondary-light);
            color: white;
        }
        
        /* Additional Info */
        .additional-info {
            padding: var(--space-16) 0;
            text-align: center;
        }
        
        .info-content {
            max-width: 600px;
            margin: 0 auto;
            padding: 0 var(--space-4);
        }
        
        .info-title {
            font-size: var(--text-2xl);
            margin-bottom: var(--space-4);
            color: var(--gray-900);
        }
        
        .info-text {
            color: var(--gray-600);
            margin-bottom: var(--space-6);
        }
        
        .redirect-notice {
            background-color: var(--gray-100);
            padding: var(--space-4);
            border-radius: var(--radius-lg);
            color: var(--gray-700);
            font-size: var(--text-sm);
        }
        
        /* Social Sharing */
        .social-sharing {
            margin-top: var(--space-8);
        }
        
        .social-title {
            font-size: var(--text-lg);
            margin-bottom: var(--space-4);
            color: var(--gray-700);
        }
        
        .social-buttons {
            display: flex;
            gap: var(--space-3);
            justify-content: center;
        }
        
        .share-button {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-decoration: none;
            font-size: var(--text-xl);
            transition: transform var(--transition-fast);
        }
        
        .share-button:hover {
            transform: scale(1.1);
        }
        
        .share-button.facebook { background-color: #1877f2; }
        .share-button.twitter { background-color: #1da1f2; }
        .share-button.linkedin { background-color: #0077b5; }
        .share-button.whatsapp { background-color: #25d366; }
        
        /* Animations */
        @keyframes successPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .hero-title {
                font-size: var(--text-4xl);
            }
            
            .hero-subtitle {
                font-size: var(--text-xl);
            }
            
            .success-icon {
                width: 80px;
                height: 80px;
                font-size: var(--text-4xl);
            }
            
            .steps-grid {
                grid-template-columns: 1fr;
                gap: var(--space-6);
            }
            
            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .cta-button {
                width: 100%;
                max-width: 300px;
                justify-content: center;
            }
        }
        
        @media (max-width: 480px) {
            .step-item {
                padding: var(--space-6);
            }
            
            .step-icon {
                width: 60px;
                height: 60px;
                font-size: var(--text-2xl);
            }
        }
    `;

    async render(context) {
        const thankYouModule = await import('../modules/thank-you.js');
        const data = thankYouModule.default.getData();
        const params = thankYouModule.default.getParams();
        
        return html`
            <!-- Hero Section -->
            <section class="hero">
                <div class="hero-content">
                    <div class="success-icon">
                        <i class="fas fa-check"></i>
                    </div>
                    <h1 class="hero-title">${data?.hero?.title || 'Merci !'}</h1>
                    <p class="hero-subtitle">${data?.hero?.subtitle || 'Votre message a été envoyé avec succès'}</p>
                    <p class="hero-description">${data?.hero?.description || 'Un de nos experts vous contactera dans les 24 heures pour discuter de votre projet.'}</p>
                </div>
            </section>

            <!-- Next Steps Section -->
            <section class="next-steps">
                <div class="container">
                    <h2 class="section-title">${data?.next_steps?.title || 'Que se passe-t-il maintenant ?'}</h2>
                    <div class="steps-grid">
                        ${this.renderSteps(data?.next_steps?.steps)}
                    </div>
                </div>
            </section>

            <!-- CTA Section -->
            <section class="cta-section">
                <div class="cta-content">
                    <h2 class="cta-title">${data?.cta?.title || 'Besoin d\'une assistance immédiate ?'}</h2>
                    <p class="cta-description">${data?.cta?.description || 'Notre équipe est disponible pour répondre à vos questions'}</p>
                    <div class="cta-buttons">
                        ${this.renderCTAButtons(data?.cta?.buttons)}
                    </div>
                </div>
            </section>

            <!-- Additional Info -->
            <section class="additional-info">
                <div class="info-content">
                    <h3 class="info-title">Partagez votre expérience</h3>
                    <p class="info-text">Aidez d'autres personnes à découvrir nos services en partageant votre expérience.</p>
                    
                    <div class="social-sharing">
                        <h4 class="social-title">Partager sur les réseaux sociaux</h4>
                        <div class="social-buttons">
                            <a href="#" class="share-button facebook" data-platform="facebook" aria-label="Partager sur Facebook">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="share-button twitter" data-platform="twitter" aria-label="Partager sur Twitter">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="share-button linkedin" data-platform="linkedin" aria-label="Partager sur LinkedIn">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                            <a href="#" class="share-button whatsapp" data-platform="whatsapp" aria-label="Partager sur WhatsApp">
                                <i class="fab fa-whatsapp"></i>
                            </a>
                        </div>
                    </div>
                    
                    <div class="redirect-notice">
                        <p>Vous serez redirigé vers la page d'accueil dans <span id="redirect-timer">10</span> secondes.</p>
                    </div>
                </div>
            </section>
        `;
    }

    renderSteps(steps) {
        const defaultSteps = [
            {
                icon: 'fas fa-envelope-open',
                title: 'Email de confirmation',
                description: 'Vous recevrez un email de confirmation sous peu'
            },
            {
                icon: 'fas fa-phone',
                title: 'Contact expert',
                description: 'Notre expert vous appellera dans les 24 heures'
            },
            {
                icon: 'fas fa-calendar',
                title: 'Consultation',
                description: 'Nous planifierons une consultation détaillée'
            }
        ];

        const stepsToRender = steps || defaultSteps;

        return stepsToRender.map(step => html`
            <div class="step-item">
                <div class="step-icon">
                    <i class="${step.icon}"></i>
                </div>
                <h3 class="step-title">${step.title}</h3>
                <p class="step-description">${step.description}</p>
            </div>
        `);
    }

    renderCTAButtons(buttons) {
        const defaultButtons = [
            { text: 'Appeler maintenant', action: 'phone', icon: 'fas fa-phone', primary: true },
            { text: 'WhatsApp', action: 'whatsapp', icon: 'fab fa-whatsapp' },
            { text: 'Retour à l\'accueil', action: 'home', icon: 'fas fa-home' }
        ];

        const buttonsToRender = buttons || defaultButtons;

        return buttonsToRender.map(button => html`
            <a href="#" class="cta-button ${button.primary ? 'primary' : ''}" data-action="${button.action}">
                ${button.icon ? html`<i class="${button.icon}"></i>` : ''}
                ${button.text}
            </a>
        `);
    }
}

export default ThankYouTemplate;
