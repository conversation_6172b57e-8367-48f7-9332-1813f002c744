// ===== 404 PAGE TEMPLATE USING LIT =====
import { html, css, LitElement } from 'lit';

class NotFoundTemplate extends LitElement {
    static styles = css`
        :host {
            display: block;
        }
        
        /* Hero Section */
        .hero {
            padding: var(--space-20) 0;
            background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .hero::before {
            content: '404';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 20rem;
            font-weight: 900;
            color: rgba(0, 0, 0, 0.05);
            z-index: 1;
            font-family: var(--font-heading);
        }
        
        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 800px;
            margin: 0 auto;
            padding: 0 var(--space-4);
        }
        
        .error-icon {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, var(--accent-color) 0%, #c53030 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--space-6);
            color: white;
            font-size: var(--text-5xl);
            animation: bounce 2s ease-in-out infinite;
        }
        
        .hero-title {
            font-size: var(--text-6xl);
            margin-bottom: var(--space-4);
            color: var(--gray-900);
            animation: fadeInUp 0.8s ease-out;
        }
        
        .hero-subtitle {
            font-size: var(--text-2xl);
            margin-bottom: var(--space-6);
            color: var(--gray-700);
            animation: fadeInUp 0.8s ease-out 0.2s both;
        }
        
        .hero-description {
            font-size: var(--text-lg);
            color: var(--gray-600);
            animation: fadeInUp 0.8s ease-out 0.4s both;
        }
        
        /* Suggestions Section */
        .suggestions {
            padding: var(--space-20) 0;
        }
        
        .section-title {
            text-align: center;
            font-size: var(--text-4xl);
            margin-bottom: var(--space-16);
            color: var(--gray-900);
        }
        
        .suggestions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: var(--space-8);
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--space-4);
        }
        
        .suggestion-item {
            background: white;
            padding: var(--space-8);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            text-align: center;
            transition: all var(--transition-normal);
            cursor: pointer;
            border: 2px solid transparent;
            animation: fadeInUp 0.8s ease-out;
        }
        
        .suggestion-item:nth-child(2) { animation-delay: 0.1s; }
        .suggestion-item:nth-child(3) { animation-delay: 0.2s; }
        .suggestion-item:nth-child(4) { animation-delay: 0.3s; }
        
        .suggestion-item:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-xl);
            border-color: var(--primary-color);
        }
        
        .suggestion-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--space-4);
            color: white;
            font-size: var(--text-3xl);
            transition: transform var(--transition-fast);
        }
        
        .suggestion-item:hover .suggestion-icon {
            transform: scale(1.1);
        }
        
        .suggestion-title {
            font-size: var(--text-xl);
            font-weight: 600;
            margin-bottom: var(--space-3);
            color: var(--gray-900);
        }
        
        .suggestion-description {
            color: var(--gray-600);
            line-height: 1.6;
        }
        
        /* Search Section */
        .search-section {
            padding: var(--space-16) 0;
            background-color: var(--gray-50);
        }
        
        .search-content {
            max-width: 600px;
            margin: 0 auto;
            padding: 0 var(--space-4);
            text-align: center;
        }
        
        .search-title {
            font-size: var(--text-3xl);
            margin-bottom: var(--space-6);
            color: var(--gray-900);
        }
        
        .search-form {
            display: flex;
            gap: var(--space-3);
            margin-bottom: var(--space-8);
        }
        
        .search-input {
            flex: 1;
            padding: var(--space-4);
            border: 2px solid var(--gray-300);
            border-radius: var(--radius-lg);
            font-size: var(--text-lg);
            transition: border-color var(--transition-fast);
        }
        
        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
        }
        
        .search-button {
            padding: var(--space-4) var(--space-6);
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--radius-lg);
            font-size: var(--text-lg);
            font-weight: 600;
            cursor: pointer;
            transition: background-color var(--transition-fast);
            white-space: nowrap;
        }
        
        .search-button:hover {
            background-color: var(--primary-dark);
        }
        
        /* Quick Links */
        .quick-links {
            display: flex;
            gap: var(--space-4);
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .quick-link {
            padding: var(--space-2) var(--space-4);
            background-color: var(--gray-200);
            color: var(--gray-700);
            border-radius: var(--radius-full);
            text-decoration: none;
            font-size: var(--text-sm);
            transition: all var(--transition-fast);
        }
        
        .quick-link:hover {
            background-color: var(--primary-color);
            color: white;
        }
        
        /* Back to Top */
        .back-to-top {
            text-align: center;
            margin-top: var(--space-8);
        }
        
        .back-to-top-button {
            display: inline-flex;
            align-items: center;
            gap: var(--space-2);
            padding: var(--space-3) var(--space-6);
            background-color: var(--secondary-color);
            color: white;
            border: none;
            border-radius: var(--radius-lg);
            text-decoration: none;
            font-weight: 600;
            transition: background-color var(--transition-fast);
        }
        
        .back-to-top-button:hover {
            background-color: var(--secondary-dark);
            color: white;
        }
        
        /* Animations */
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .hero::before {
                font-size: 12rem;
            }
            
            .hero-title {
                font-size: var(--text-4xl);
            }
            
            .hero-subtitle {
                font-size: var(--text-xl);
            }
            
            .error-icon {
                width: 100px;
                height: 100px;
                font-size: var(--text-4xl);
            }
            
            .suggestions-grid {
                grid-template-columns: 1fr;
                gap: var(--space-6);
            }
            
            .search-form {
                flex-direction: column;
            }
            
            .quick-links {
                flex-direction: column;
                align-items: center;
            }
        }
        
        @media (max-width: 480px) {
            .hero::before {
                font-size: 8rem;
            }
            
            .suggestion-item {
                padding: var(--space-6);
            }
            
            .suggestion-icon {
                width: 60px;
                height: 60px;
                font-size: var(--text-2xl);
            }
        }
    `;

    async render(context) {
        const notFoundModule = await import('../modules/404.js');
        const data = notFoundModule.default.getData();
        
        return html`
            <!-- Hero Section -->
            <section class="hero">
                <div class="hero-content">
                    <div class="error-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h1 class="hero-title">${data?.hero?.title || 'Page non trouvée'}</h1>
                    <p class="hero-subtitle">${data?.hero?.subtitle || 'La page que vous recherchez n\'existe pas'}</p>
                    <p class="hero-description">${data?.hero?.description || 'La page a peut-être été déplacée, supprimée, ou vous avez saisi une URL incorrecte.'}</p>
                </div>
            </section>

            <!-- Suggestions Section -->
            <section class="suggestions">
                <div class="container">
                    <h2 class="section-title">${data?.suggestions?.title || 'Que pouvez-vous faire ?'}</h2>
                    <div class="suggestions-grid">
                        ${this.renderSuggestions(data?.suggestions?.items)}
                    </div>
                </div>
            </section>

            <!-- Search Section -->
            <section class="search-section">
                <div class="search-content">
                    <h3 class="search-title">${data?.search?.title || 'Rechercher sur notre site'}</h3>
                    <form id="search-form" class="search-form">
                        <input 
                            type="text" 
                            id="search-input" 
                            class="search-input" 
                            placeholder="${data?.search?.placeholder || 'Que recherchez-vous ?'}"
                            required
                        >
                        <button type="submit" class="search-button">
                            <i class="fas fa-search"></i>
                            ${data?.search?.button || 'Rechercher'}
                        </button>
                    </form>
                    
                    <div class="quick-links">
                        <a href="#" class="quick-link nav-button" data-route="citizenship">Citoyenneté</a>
                        <a href="#" class="quick-link nav-button" data-route="residency">Résidence</a>
                        <a href="#" class="quick-link nav-button" data-route="services">Services</a>
                        <a href="#" class="quick-link nav-button" data-route="about">À propos</a>
                        <a href="#" class="quick-link nav-button" data-route="contact">Contact</a>
                    </div>
                    
                    <div class="back-to-top">
                        <a href="#" class="back-to-top-button nav-button" data-route="home">
                            <i class="fas fa-home"></i>
                            Retour à l'accueil
                        </a>
                    </div>
                </div>
            </section>
        `;
    }

    renderSuggestions(suggestions) {
        const defaultSuggestions = [
            {
                icon: 'fas fa-home',
                title: 'Aller à l\'accueil',
                description: 'Retournez à notre page d\'accueil pour trouver ce dont vous avez besoin',
                route: 'home'
            },
            {
                icon: 'fas fa-passport',
                title: 'Programmes de Citoyenneté',
                description: 'Explorez nos programmes de citoyenneté par investissement',
                route: 'citizenship'
            },
            {
                icon: 'fas fa-map-marker-alt',
                title: 'Programmes de Résidence',
                description: 'Découvrez nos options de résidence par investissement',
                route: 'residency'
            },
            {
                icon: 'fas fa-phone',
                title: 'Nous contacter',
                description: 'Entrez en contact avec nos experts',
                route: 'contact'
            }
        ];

        const suggestionsToRender = suggestions || defaultSuggestions;

        return suggestionsToRender.map(suggestion => html`
            <div class="suggestion-item nav-button" data-route="${suggestion.route}">
                <div class="suggestion-icon">
                    <i class="${suggestion.icon}"></i>
                </div>
                <h3 class="suggestion-title">${suggestion.title}</h3>
                <p class="suggestion-description">${suggestion.description}</p>
            </div>
        `);
    }
}

export default NotFoundTemplate;
