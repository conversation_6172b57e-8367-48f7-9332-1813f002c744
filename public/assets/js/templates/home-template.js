// ===== HOME PAGE TEMPLATE USING LIT =====
import { html, css, LitElement } from 'lit';

class HomeTemplate extends LitElement {
    static styles = css`
        :host {
            display: block;
        }
        
        /* Hero Section */
        .hero {
            position: relative;
            height: 100vh;
            min-height: 600px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            overflow: hidden;
        }
        
        .hero-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: url('/assets/img/hero-bg.jpg');
            background-size: cover;
            background-position: center;
            opacity: 0.3;
        }
        
        .hero-content {
            position: relative;
            z-index: 2;
            text-align: center;
            max-width: 800px;
            padding: 0 var(--space-4);
        }
        
        .hero-title {
            font-size: var(--text-6xl);
            font-weight: 700;
            margin-bottom: var(--space-6);
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.5s forwards;
        }
        
        .hero-subtitle {
            font-size: var(--text-2xl);
            margin-bottom: var(--space-8);
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.7s forwards;
        }
        
        .hero-cta {
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.9s forwards;
        }
        
        /* Stats Section */
        .stats {
            padding: var(--space-20) 0;
            background-color: var(--gray-50);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-8);
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--space-4);
        }
        
        .stat-item {
            text-align: center;
            padding: var(--space-6);
        }
        
        .stat-number {
            font-size: var(--text-5xl);
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: var(--space-2);
        }
        
        .stat-label {
            font-size: var(--text-lg);
            color: var(--gray-600);
        }
        
        /* Featured Countries */
        .featured-countries {
            padding: var(--space-20) 0;
        }
        
        .section-title {
            text-align: center;
            margin-bottom: var(--space-16);
        }
        
        .countries-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--space-8);
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--space-4);
        }
        
        .country-card {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            transition: all var(--transition-normal);
            cursor: pointer;
        }
        
        .country-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-xl);
        }
        
        .country-image {
            height: 200px;
            background-size: cover;
            background-position: center;
        }
        
        .country-content {
            padding: var(--space-6);
        }
        
        .country-name {
            font-size: var(--text-2xl);
            font-weight: 600;
            margin-bottom: var(--space-2);
        }
        
        .country-type {
            display: inline-block;
            padding: var(--space-1) var(--space-3);
            background-color: var(--secondary-color);
            color: white;
            border-radius: var(--radius-full);
            font-size: var(--text-sm);
            margin-bottom: var(--space-4);
        }
        
        .country-details {
            display: flex;
            justify-content: space-between;
            margin-bottom: var(--space-4);
        }
        
        .detail-item {
            text-align: center;
        }
        
        .detail-label {
            font-size: var(--text-sm);
            color: var(--gray-500);
            margin-bottom: var(--space-1);
        }
        
        .detail-value {
            font-weight: 600;
            color: var(--gray-900);
        }
        
        /* Why Choose Us */
        .why-choose-us {
            padding: var(--space-20) 0;
            background-color: var(--gray-50);
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--space-8);
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--space-4);
        }
        
        .feature-item {
            text-align: center;
            padding: var(--space-6);
        }
        
        .feature-icon {
            width: 80px;
            height: 80px;
            background-color: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--space-4);
            color: white;
            font-size: var(--text-3xl);
        }
        
        .feature-title {
            font-size: var(--text-xl);
            font-weight: 600;
            margin-bottom: var(--space-3);
        }
        
        /* CTA Section */
        .cta-section {
            padding: var(--space-20) 0;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            text-align: center;
        }
        
        .cta-content {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 var(--space-4);
        }
        
        .cta-title {
            font-size: var(--text-4xl);
            margin-bottom: var(--space-6);
        }
        
        .cta-buttons {
            display: flex;
            gap: var(--space-4);
            justify-content: center;
            flex-wrap: wrap;
        }
        
        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .animate-on-scroll {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease-out;
        }
        
        .animate-on-scroll.animate-in {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .hero-title {
                font-size: var(--text-4xl);
            }
            
            .hero-subtitle {
                font-size: var(--text-xl);
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: var(--space-4);
            }
            
            .countries-grid {
                grid-template-columns: 1fr;
            }
            
            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    `;

    async render(context) {
        const homeModule = await import('../modules/home.js');
        const data = homeModule.default.getData();
        
        return html`
            <!-- Hero Section -->
            <section class="hero">
                <div class="hero-background"></div>
                <div class="hero-content">
                    <h1 class="hero-title">${data?.hero?.title || 'Investissez dans votre avenir'}</h1>
                    <p class="hero-subtitle">${data?.hero?.subtitle || 'Accédez à la liberté mondiale'}</p>
                    <div class="hero-cta">
                        <button class="btn btn-secondary btn-lg cta-button" data-action="discover-programs">
                            ${data?.hero?.cta || 'Découvrez nos programmes'}
                        </button>
                    </div>
                </div>
            </section>

            <!-- Stats Section -->
            <section class="stats animate-on-scroll">
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number counter" data-target="${data?.stats?.countries || 25}">0</div>
                        <div class="stat-label">Pays partenaires</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number counter" data-target="${data?.stats?.clients || 1000}">0</div>
                        <div class="stat-label">Clients satisfaits</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number counter" data-target="${data?.stats?.success_rate || 98}">0</div>
                        <div class="stat-label">% de réussite</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number counter" data-target="${data?.stats?.experience || 15}">0</div>
                        <div class="stat-label">Années d'expérience</div>
                    </div>
                </div>
            </section>

            <!-- Featured Countries -->
            <section class="featured-countries animate-on-scroll">
                <div class="container">
                    <h2 class="section-title">Programmes populaires</h2>
                    <div class="countries-grid">
                        ${this.renderCountryCards(data?.featured_countries || [])}
                    </div>
                </div>
            </section>

            <!-- Why Choose Us -->
            <section class="why-choose-us animate-on-scroll">
                <div class="container">
                    <h2 class="section-title">Pourquoi nous choisir ?</h2>
                    <div class="features-grid">
                        ${this.renderFeatures()}
                    </div>
                </div>
            </section>

            <!-- CTA Section -->
            <section class="cta-section animate-on-scroll">
                <div class="cta-content">
                    <h2 class="cta-title">Prêt à commencer votre parcours ?</h2>
                    <p>Contactez nos experts pour une consultation gratuite et personnalisée.</p>
                    <div class="cta-buttons">
                        <button class="btn btn-secondary btn-lg cta-button" data-action="consultation">
                            Consultation gratuite
                        </button>
                        <button class="btn btn-outline btn-lg cta-button" data-action="contact">
                            Nous contacter
                        </button>
                    </div>
                </div>
            </section>
        `;
    }

    renderCountryCards(countries) {
        return countries.map(country => html`
            <div class="country-card" data-country="${country.name.toLowerCase()}" data-type="${country.type}">
                <div class="country-image" style="background-image: url('/assets/img/countries/${country.name.toLowerCase()}.jpg')"></div>
                <div class="country-content">
                    <h3 class="country-name">${country.name}</h3>
                    <span class="country-type">${country.type === 'citizenship' ? 'Citoyenneté' : 'Résidence'}</span>
                    <div class="country-details">
                        <div class="detail-item">
                            <div class="detail-label">Investissement</div>
                            <div class="detail-value">${country.investment}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Délai</div>
                            <div class="detail-value">${country.processing_time}</div>
                        </div>
                    </div>
                    <button class="btn btn-primary">En savoir plus</button>
                </div>
            </div>
        `);
    }

    renderFeatures() {
        const features = [
            {
                icon: 'fas fa-award',
                title: 'Expertise reconnue',
                description: '15 ans d\'expérience dans l\'immigration par investissement'
            },
            {
                icon: 'fas fa-globe',
                title: 'Réseau mondial',
                description: 'Présence dans plus de 25 pays à travers le monde'
            },
            {
                icon: 'fas fa-clock',
                title: 'Processus rapide',
                description: 'Délais optimisés grâce à notre expertise'
            },
            {
                icon: 'fas fa-shield-alt',
                title: 'Conformité légale',
                description: 'Respect strict de toutes les réglementations'
            }
        ];

        return features.map(feature => html`
            <div class="feature-item">
                <div class="feature-icon">
                    <i class="${feature.icon}"></i>
                </div>
                <h3 class="feature-title">${feature.title}</h3>
                <p>${feature.description}</p>
            </div>
        `);
    }
}

export default HomeTemplate;
