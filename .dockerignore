# CitizenshipPro Docker Ignore File

# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
NEXT_STEPS.md
*.md
docs/

# Development files
.env
.env.local
.env.development
.env.staging
.env.production

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.sublime-project
.sublime-workspace

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Node.js (if using build tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Logs
*.log
logs/
backend/mail/logs/*.log

# Cache
backend/lang/cache/*.json
cache/
.cache/

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# Backup files
*.bak
*.backup
*.old

# Test files
test.php
tests/
coverage/

# Docker files (avoid recursion)
Dockerfile*
docker-compose*.yml
.dockerignore

# Build outputs
dist/
build/

# Vendor (will be installed during build)
vendor/

# Database files
*.sqlite
*.db

# Uploads
uploads/
public/uploads/

# SSL certificates
*.pem
*.key
*.crt
*.csr

# Package files
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Local development
local/
dev/
development/

# Staging and production configs
staging/
production/

# Performance monitoring
newrelic.ini
blackfire.ini

# Kubernetes
k8s/

# Terraform
*.tfstate
*.tfstate.backup
.terraform/

# Vagrant
.vagrant/
