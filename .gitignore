# CitizenshipPro - Git Ignore File

# Environment and Configuration
.env
.env.local
.env.production
.env.staging
config.local.php

# Composer
/vendor/
composer.phar
composer.lock

# Cache and Logs
backend/lang/cache/*.json
backend/mail/logs/*.log
*.log
logs/
cache/

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~
.sublime-project
.sublime-workspace

# Node.js (if using build tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Build outputs
dist/
build/
public/build/

# Backup files
*.bak
*.backup
*.old
*~

# Database
*.sqlite
*.db
*.sql

# Uploads and user content
uploads/
user-content/
public/uploads/

# SSL certificates
*.pem
*.key
*.crt
*.csr

# Email templates (if generated)
backend/mail/templates/generated/

# Analytics and tracking
analytics/
tracking/

# Documentation builds
docs/_build/
docs/build/

# Test coverage
coverage/
.nyc_output/
.coverage

# Package files
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Images (keep placeholders, ignore actual content)
public/assets/img/countries/*.jpg
public/assets/img/countries/*.png
public/assets/img/countries/*.webp
public/assets/img/testimonials/*.jpg
public/assets/img/testimonials/*.png
public/assets/img/hero-bg.jpg
public/assets/img/hero-bg.png
public/assets/img/hero-bg.webp

# Keep placeholder files
!public/assets/img/.gitkeep
!public/assets/img/countries/.gitkeep
!public/assets/img/testimonials/.gitkeep

# Fonts (if not using CDN)
public/assets/fonts/*.woff
public/assets/fonts/*.woff2
public/assets/fonts/*.ttf
public/assets/fonts/*.otf

# Minified files (if using build process)
*.min.js
*.min.css

# Source maps
*.map

# PHP specific
*.php~
.phpunit.result.cache

# Security
.htpasswd
.htaccess.backup
wp-config.php

# Local development
local/
dev/
development/

# Staging and production
staging/
production/

# Email queue (if implemented)
email-queue/

# Session files
sessions/
tmp/sessions/

# Error logs
error.log
error_log
php_errors.log

# Access logs
access.log
access_log

# Maintenance
maintenance.html
maintenance.php

# Backup directories
backups/
backup/

# Migration files (if using database)
migrations/data/

# API keys and secrets
api-keys.txt
secrets.txt
credentials.json

# Local configuration overrides
local.config.php
local.settings.php

# Performance monitoring
newrelic.ini
blackfire.ini

# Docker (if using containerization)
docker-compose.override.yml
.dockerignore

# Kubernetes (if using orchestration)
k8s/secrets/
k8s/local/

# Terraform (if using infrastructure as code)
*.tfstate
*.tfstate.backup
.terraform/

# Vagrant (if using virtual machines)
.vagrant/

# JetBrains IDEs
.idea/
*.iml
*.iws

# Visual Studio Code
.vscode/settings.json
.vscode/tasks.json
.vscode/launch.json
.vscode/extensions.json

# Sublime Text
*.sublime-project
*.sublime-workspace

# Atom
.atom/

# Vim
*.swp
*.swo
.netrwhist

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Local testing
test-results/
test-output/

# Performance testing
performance-results/

# Load testing
load-test-results/

# Security scanning
security-scan-results/

# Code quality reports
quality-reports/

# Dependency scanning
dependency-scan-results/
