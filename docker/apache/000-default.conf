<VirtualHost *:80>
    # CitizenshipPro Apache Virtual Host Configuration
    
    ServerAdmin <EMAIL>
    DocumentRoot /var/www/html/public
    
    # Server name (change in production)
    ServerName localhost
    
    # Directory configuration
    <Directory /var/www/html/public>
        Options -Indexes +FollowSymLinks
        AllowOverride All
        Require all granted
        
        # Security headers
        Header always set X-Content-Type-Options nosniff
        Header always set X-XSS-Protection "1; mode=block"
        Header always set X-Frame-Options DENY
        Header always set Referrer-Policy "strict-origin-when-cross-origin"
        
        # Remove server signature
        Header always unset Server
        Header always unset X-Powered-By
    </Directory>
    
    # Backend API access
    <Directory /var/www/html/backend>
        Options -Indexes
        AllowOverride None
        Require all granted
        
        # Only allow access to specific files
        <FilesMatch "\.(php)$">
            Require all granted
        </FilesMatch>
        
        <FilesMatch "^(?!.*\.(php)$).*$">
            Require all denied
        </FilesMatch>
    </Directory>
    
    # Deny access to sensitive files
    <FilesMatch "\.(htaccess|htpasswd|ini|log|sh|sql|conf|bak|env)$">
        Require all denied
    </FilesMatch>
    
    # Alias for backend API
    Alias /backend /var/www/html/backend
    
    # Logging
    ErrorLog ${APACHE_LOG_DIR}/citizenshippro_error.log
    CustomLog ${APACHE_LOG_DIR}/citizenshippro_access.log combined
    
    # Compression
    <IfModule mod_deflate.c>
        AddOutputFilterByType DEFLATE text/plain
        AddOutputFilterByType DEFLATE text/html
        AddOutputFilterByType DEFLATE text/xml
        AddOutputFilterByType DEFLATE text/css
        AddOutputFilterByType DEFLATE application/xml
        AddOutputFilterByType DEFLATE application/xhtml+xml
        AddOutputFilterByType DEFLATE application/rss+xml
        AddOutputFilterByType DEFLATE application/javascript
        AddOutputFilterByType DEFLATE application/x-javascript
        AddOutputFilterByType DEFLATE application/json
    </IfModule>
    
    # Browser caching
    <IfModule mod_expires.c>
        ExpiresActive On
        
        # Images
        ExpiresByType image/jpg "access plus 1 month"
        ExpiresByType image/jpeg "access plus 1 month"
        ExpiresByType image/gif "access plus 1 month"
        ExpiresByType image/png "access plus 1 month"
        ExpiresByType image/svg+xml "access plus 1 month"
        ExpiresByType image/webp "access plus 1 month"
        ExpiresByType image/x-icon "access plus 1 year"
        
        # CSS and JavaScript
        ExpiresByType text/css "access plus 1 month"
        ExpiresByType application/javascript "access plus 1 month"
        ExpiresByType application/x-javascript "access plus 1 month"
        
        # Fonts
        ExpiresByType font/woff "access plus 1 year"
        ExpiresByType font/woff2 "access plus 1 year"
        ExpiresByType application/font-woff "access plus 1 year"
        ExpiresByType application/font-woff2 "access plus 1 year"
        
        # HTML
        ExpiresByType text/html "access plus 1 hour"
        
        # JSON API responses
        ExpiresByType application/json "access plus 1 hour"
    </IfModule>
</VirtualHost>
