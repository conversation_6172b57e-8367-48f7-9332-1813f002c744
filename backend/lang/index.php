<?php
/**
 * Multilingual Content API
 * Serves content in JSON format for the SPA
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Get parameters
$lang = $_GET['lang'] ?? 'fr';
$page = $_GET['page'] ?? 'home';

// Validate language
$supportedLangs = ['fr', 'en'];
if (!in_array($lang, $supportedLangs)) {
    $lang = 'fr';
}

// Sanitize page parameter
$page = preg_replace('/[^a-zA-Z0-9_-]/', '', $page);

try {
    // Check cache first
    $cacheFile = __DIR__ . "/cache/{$lang}_{$page}.json";
    $cacheTime = 3600; // 1 hour cache
    
    if (file_exists($cacheFile) && (time() - filemtime($cacheFile)) < $cacheTime) {
        // Serve from cache
        $content = file_get_contents($cacheFile);
        echo $content;
        exit();
    }
    
    // Load language file
    $langFile = __DIR__ . "/{$lang}.php";
    if (!file_exists($langFile)) {
        throw new Exception("Language file not found: {$lang}");
    }
    
    include $langFile;
    
    // Get content for the specific page
    $content = getPageContent($page, $translations);
    
    // Cache the result
    if (!is_dir(__DIR__ . '/cache')) {
        mkdir(__DIR__ . '/cache', 0755, true);
    }
    
    $jsonContent = json_encode($content, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    file_put_contents($cacheFile, $jsonContent);
    
    // Return content
    echo $jsonContent;
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Internal server error',
        'message' => $e->getMessage()
    ]);
}

/**
 * Get content for a specific page
 */
function getPageContent($page, $translations) {
    switch ($page) {
        case 'home':
            return [
                'hero' => $translations['home']['hero'],
                'stats' => $translations['home']['stats'],
                'featured_countries' => $translations['home']['featured_countries'],
                'features' => $translations['home']['features'],
                'cta' => $translations['home']['cta']
            ];
            
        case 'citizenship':
            return [
                'hero' => $translations['citizenship']['hero'],
                'countries' => $translations['citizenship']['countries'],
                'benefits' => $translations['citizenship']['benefits'],
                'process' => $translations['citizenship']['process']
            ];
            
        case 'residency':
            return [
                'hero' => $translations['residency']['hero'],
                'countries' => $translations['residency']['countries'],
                'benefits' => $translations['residency']['benefits'],
                'process' => $translations['residency']['process']
            ];
            
        case 'services':
            return [
                'hero' => $translations['services']['hero'],
                'services' => $translations['services']['services'],
                'process' => $translations['services']['process']
            ];
            
        case 'about':
            return [
                'hero' => $translations['about']['hero'],
                'mission' => $translations['about']['mission'],
                'team' => $translations['about']['team'],
                'offices' => $translations['about']['offices']
            ];
            
        case 'blog':
            return [
                'hero' => $translations['blog']['hero'],
                'articles' => $translations['blog']['articles']
            ];
            
        case 'contact':
            return [
                'hero' => $translations['contact']['hero'],
                'form' => $translations['contact']['form'],
                'info' => $translations['contact']['info']
            ];
            
        case 'common':
            return [
                'nav' => $translations['nav'],
                'footer' => $translations['footer'],
                'common' => $translations['common']
            ];
            
        default:
            return [
                'error' => 'Page not found',
                'available_pages' => ['home', 'citizenship', 'residency', 'services', 'about', 'blog', 'contact', 'common']
            ];
    }
}

/**
 * Clear cache for a specific language and page
 */
function clearCache($lang = null, $page = null) {
    $cacheDir = __DIR__ . '/cache';
    
    if (!is_dir($cacheDir)) {
        return;
    }
    
    if ($lang && $page) {
        $cacheFile = "{$cacheDir}/{$lang}_{$page}.json";
        if (file_exists($cacheFile)) {
            unlink($cacheFile);
        }
    } else {
        // Clear all cache
        $files = glob("{$cacheDir}/*.json");
        foreach ($files as $file) {
            unlink($file);
        }
    }
}

/**
 * Get available languages
 */
function getAvailableLanguages() {
    $langFiles = glob(__DIR__ . '/*.php');
    $languages = [];
    
    foreach ($langFiles as $file) {
        $filename = basename($file, '.php');
        if ($filename !== 'index') {
            $languages[] = $filename;
        }
    }
    
    return $languages;
}

/**
 * Validate and sanitize input
 */
function sanitizeInput($input, $type = 'string') {
    switch ($type) {
        case 'string':
            return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
        case 'email':
            return filter_var(trim($input), FILTER_VALIDATE_EMAIL);
        case 'phone':
            return preg_replace('/[^0-9+\-\s\(\)]/', '', trim($input));
        case 'alphanumeric':
            return preg_replace('/[^a-zA-Z0-9_-]/', '', trim($input));
        default:
            return trim($input);
    }
}

/**
 * Log API requests for analytics
 */
function logRequest($lang, $page, $userAgent = '') {
    $logFile = __DIR__ . '/logs/api_requests.log';
    $logDir = dirname($logFile);
    
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
    
    $logEntry = "[{$timestamp}] {$ip} - {$lang}/{$page} - {$userAgent}\n";
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

// Log this request
logRequest($lang, $page);
?>
