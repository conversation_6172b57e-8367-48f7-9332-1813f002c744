<?php
/**
 * SMTP Configuration for <PERSON><PERSON><PERSON><PERSON><PERSON>
 * Configure your email settings here
 */

// SMTP Configuration
$smtpConfig = [
    // SMTP Server Settings
    'host' => 'smtp.gmail.com', // Change to your SMTP server
    'port' => 587,
    'encryption' => 'tls', // 'tls' or 'ssl'
    'auth' => true,
    
    // SMTP Authentication
    'username' => '<EMAIL>', // Your email
    'password' => 'your-app-password', // Your email password or app password
    
    // Email Settings
    'from_email' => '<EMAIL>',
    'from_name' => 'CitizenshipPro',
    'admin_email' => '<EMAIL>', // Where contact forms are sent
    
    // Additional Settings
    'timeout' => 30,
    'debug' => 0, // 0 = off, 1 = client messages, 2 = client and server messages
];

/**
 * Create and configure PHPMailer instance
 */
function createMailer() {
    global $smtpConfig;
    
    // Check if PHPMailer is available
    if (!class_exists('<PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer')) {
        // Try to load <PERSON><PERSON><PERSON>ailer from vendor directory
        $phpmailerPath = __DIR__ . '/../../vendor/phpmailer/phpmailer/src/PHPMailer.php';
        if (file_exists($phpmailerPath)) {
            require_once __DIR__ . '/../../vendor/phpmailer/phpmailer/src/Exception.php';
            require_once __DIR__ . '/../../vendor/phpmailer/phpmailer/src/PHPMailer.php';
            require_once __DIR__ . '/../../vendor/phpmailer/phpmailer/src/SMTP.php';
        } else {
            throw new Exception('PHPMailer not found. Please install it via Composer.');
        }
    }
    
    $mail = new PHPMailer\PHPMailer\PHPMailer(true);
    
    try {
        // Server settings
        $mail->isSMTP();
        $mail->Host = $smtpConfig['host'];
        $mail->SMTPAuth = $smtpConfig['auth'];
        $mail->Username = $smtpConfig['username'];
        $mail->Password = $smtpConfig['password'];
        $mail->SMTPSecure = $smtpConfig['encryption'];
        $mail->Port = $smtpConfig['port'];
        $mail->Timeout = $smtpConfig['timeout'];
        
        // Debug settings
        $mail->SMTPDebug = $smtpConfig['debug'];
        
        // Character set
        $mail->CharSet = 'UTF-8';
        
        return $mail;
        
    } catch (Exception $e) {
        throw new Exception("Mailer configuration failed: " . $e->getMessage());
    }
}

/**
 * Test SMTP connection
 */
function testSMTPConnection() {
    try {
        $mail = createMailer();
        $mail->smtpConnect();
        $mail->smtpClose();
        return true;
    } catch (Exception $e) {
        error_log("SMTP connection test failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Send test email
 */
function sendTestEmail($toEmail, $toName = 'Test User') {
    global $smtpConfig;
    
    try {
        $mail = createMailer();
        
        // Recipients
        $mail->setFrom($smtpConfig['from_email'], $smtpConfig['from_name']);
        $mail->addAddress($toEmail, $toName);
        
        // Content
        $mail->isHTML(true);
        $mail->Subject = 'Test Email from CitizenshipPro';
        $mail->Body = '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Test Email</title>
        </head>
        <body>
            <h1>Test Email</h1>
            <p>This is a test email from CitizenshipPro.</p>
            <p>If you receive this email, your SMTP configuration is working correctly.</p>
            <p>Timestamp: ' . date('Y-m-d H:i:s') . '</p>
        </body>
        </html>';
        
        $mail->AltBody = 'This is a test email from CitizenshipPro. If you receive this email, your SMTP configuration is working correctly. Timestamp: ' . date('Y-m-d H:i:s');
        
        return $mail->send();
        
    } catch (Exception $e) {
        error_log("Test email failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Validate SMTP configuration
 */
function validateSMTPConfig() {
    global $smtpConfig;
    
    $errors = [];
    
    // Check required fields
    $requiredFields = ['host', 'port', 'username', 'password', 'from_email', 'admin_email'];
    foreach ($requiredFields as $field) {
        if (empty($smtpConfig[$field])) {
            $errors[] = "Missing required SMTP configuration: {$field}";
        }
    }
    
    // Validate email addresses
    if (!filter_var($smtpConfig['from_email'], FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Invalid from_email address";
    }
    
    if (!filter_var($smtpConfig['admin_email'], FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Invalid admin_email address";
    }
    
    // Validate port
    if (!is_numeric($smtpConfig['port']) || $smtpConfig['port'] < 1 || $smtpConfig['port'] > 65535) {
        $errors[] = "Invalid port number";
    }
    
    // Validate encryption
    if (!in_array($smtpConfig['encryption'], ['tls', 'ssl', ''])) {
        $errors[] = "Invalid encryption type. Use 'tls', 'ssl', or leave empty";
    }
    
    return $errors;
}

/**
 * Get common SMTP configurations for popular providers
 */
function getCommonSMTPConfigs() {
    return [
        'gmail' => [
            'host' => 'smtp.gmail.com',
            'port' => 587,
            'encryption' => 'tls',
            'note' => 'Use App Password instead of regular password'
        ],
        'outlook' => [
            'host' => 'smtp-mail.outlook.com',
            'port' => 587,
            'encryption' => 'tls',
            'note' => 'Works with Outlook.com and Hotmail'
        ],
        'yahoo' => [
            'host' => 'smtp.mail.yahoo.com',
            'port' => 587,
            'encryption' => 'tls',
            'note' => 'Requires App Password'
        ],
        'sendgrid' => [
            'host' => 'smtp.sendgrid.net',
            'port' => 587,
            'encryption' => 'tls',
            'note' => 'Use "apikey" as username and your API key as password'
        ],
        'mailgun' => [
            'host' => 'smtp.mailgun.org',
            'port' => 587,
            'encryption' => 'tls',
            'note' => 'Use your Mailgun SMTP credentials'
        ]
    ];
}

// Validate configuration on load
$configErrors = validateSMTPConfig();
if (!empty($configErrors)) {
    error_log("SMTP Configuration Errors: " . implode(', ', $configErrors));
}
?>
