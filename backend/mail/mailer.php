<?php
/**
 * Contact Form Mailer
 * Handles form submissions and sends emails using PHPMailer
 */

require_once __DIR__ . '/smtp.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

try {
    // Get JSON input
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data) {
        throw new Exception('Invalid JSON data');
    }
    
    // Validate required fields
    $requiredFields = ['name', 'email', 'message'];
    foreach ($requiredFields as $field) {
        if (empty($data[$field])) {
            throw new Exception("Field '{$field}' is required");
        }
    }
    
    // Sanitize input data
    $formData = [
        'name' => sanitizeInput($data['name']),
        'email' => sanitizeInput($data['email'], 'email'),
        'phone' => sanitizeInput($data['phone'] ?? '', 'phone'),
        'country' => sanitizeInput($data['country'] ?? ''),
        'program' => sanitizeInput($data['program'] ?? ''),
        'message' => sanitizeInput($data['message']),
        'type' => sanitizeInput($data['type'] ?? 'contact'),
        'lang' => sanitizeInput($data['lang'] ?? 'fr', 'alphanumeric'),
        'timestamp' => $data['timestamp'] ?? date('c'),
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ];
    
    // Additional validation
    if (!filter_var($formData['email'], FILTER_VALIDATE_EMAIL)) {
        throw new Exception('Invalid email address');
    }
    
    if (strlen($formData['name']) < 2) {
        throw new Exception('Name must be at least 2 characters long');
    }
    
    if (strlen($formData['message']) < 10) {
        throw new Exception('Message must be at least 10 characters long');
    }
    
    // Check for spam (basic protection)
    if (isSpam($formData)) {
        throw new Exception('Spam detected');
    }
    
    // Send emails
    $emailSent = sendContactEmail($formData);
    $confirmationSent = sendConfirmationEmail($formData);
    
    if ($emailSent) {
        // Log the submission
        logSubmission($formData);
        
        echo json_encode([
            'success' => true,
            'message' => 'Message sent successfully',
            'confirmation_sent' => $confirmationSent
        ]);
    } else {
        throw new Exception('Failed to send email');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
    
    // Log error
    error_log("Contact form error: " . $e->getMessage());
}

/**
 * Send contact email to admin
 */
function sendContactEmail($data) {
    global $smtpConfig;
    
    try {
        $mail = createMailer();
        
        // Recipients
        $mail->setFrom($smtpConfig['from_email'], $smtpConfig['from_name']);
        $mail->addAddress($smtpConfig['admin_email'], 'CitizenshipPro Admin');
        $mail->addReplyTo($data['email'], $data['name']);
        
        // Content
        $mail->isHTML(true);
        $mail->Subject = 'Nouveau message de contact - ' . $data['name'];
        $mail->Body = generateContactEmailTemplate($data);
        $mail->AltBody = generateContactEmailText($data);
        
        return $mail->send();
        
    } catch (Exception $e) {
        error_log("Failed to send contact email: " . $e->getMessage());
        return false;
    }
}

/**
 * Send confirmation email to user
 */
function sendConfirmationEmail($data) {
    global $smtpConfig;
    
    try {
        $mail = createMailer();
        
        // Recipients
        $mail->setFrom($smtpConfig['from_email'], $smtpConfig['from_name']);
        $mail->addAddress($data['email'], $data['name']);
        
        // Content
        $mail->isHTML(true);
        
        if ($data['lang'] === 'en') {
            $mail->Subject = 'Thank you for contacting CitizenshipPro';
            $mail->Body = generateConfirmationEmailTemplate($data, 'en');
        } else {
            $mail->Subject = 'Merci de nous avoir contactés - CitizenshipPro';
            $mail->Body = generateConfirmationEmailTemplate($data, 'fr');
        }
        
        return $mail->send();
        
    } catch (Exception $e) {
        error_log("Failed to send confirmation email: " . $e->getMessage());
        return false;
    }
}

/**
 * Generate HTML email template for contact form
 */
function generateContactEmailTemplate($data) {
    $programText = !empty($data['program']) ? "<tr><td><strong>Programme d'intérêt:</strong></td><td>{$data['program']}</td></tr>" : '';
    $phoneText = !empty($data['phone']) ? "<tr><td><strong>Téléphone:</strong></td><td>{$data['phone']}</td></tr>" : '';
    $countryText = !empty($data['country']) ? "<tr><td><strong>Pays:</strong></td><td>{$data['country']}</td></tr>" : '';
    
    return "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <title>Nouveau message de contact</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #1a365d; color: white; padding: 20px; text-align: center; }
            .content { background: #f9f9f9; padding: 20px; }
            table { width: 100%; border-collapse: collapse; }
            td { padding: 10px; border-bottom: 1px solid #ddd; }
            .message { background: white; padding: 15px; border-radius: 5px; margin-top: 15px; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>Nouveau message de contact</h1>
            </div>
            <div class='content'>
                <table>
                    <tr><td><strong>Nom:</strong></td><td>{$data['name']}</td></tr>
                    <tr><td><strong>Email:</strong></td><td>{$data['email']}</td></tr>
                    {$phoneText}
                    {$countryText}
                    {$programText}
                    <tr><td><strong>Date:</strong></td><td>{$data['timestamp']}</td></tr>
                    <tr><td><strong>IP:</strong></td><td>{$data['ip']}</td></tr>
                </table>
                <div class='message'>
                    <strong>Message:</strong><br>
                    " . nl2br(htmlspecialchars($data['message'])) . "
                </div>
            </div>
        </div>
    </body>
    </html>";
}

/**
 * Generate plain text email for contact form
 */
function generateContactEmailText($data) {
    $text = "Nouveau message de contact\n\n";
    $text .= "Nom: {$data['name']}\n";
    $text .= "Email: {$data['email']}\n";
    
    if (!empty($data['phone'])) {
        $text .= "Téléphone: {$data['phone']}\n";
    }
    
    if (!empty($data['country'])) {
        $text .= "Pays: {$data['country']}\n";
    }
    
    if (!empty($data['program'])) {
        $text .= "Programme d'intérêt: {$data['program']}\n";
    }
    
    $text .= "Date: {$data['timestamp']}\n";
    $text .= "IP: {$data['ip']}\n\n";
    $text .= "Message:\n{$data['message']}";
    
    return $text;
}

/**
 * Generate confirmation email template
 */
function generateConfirmationEmailTemplate($data, $lang = 'fr') {
    if ($lang === 'en') {
        return generateConfirmationEmailEN($data);
    } else {
        return generateConfirmationEmailFR($data);
    }
}

function generateConfirmationEmailFR($data) {
    return "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <title>Merci pour votre message</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #1a365d; color: white; padding: 20px; text-align: center; }
            .content { background: #f9f9f9; padding: 20px; }
            .cta { background: #d4af37; color: white; padding: 15px; text-align: center; margin: 20px 0; border-radius: 5px; }
            .cta a { color: white; text-decoration: none; font-weight: bold; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>Merci pour votre message !</h1>
            </div>
            <div class='content'>
                <p>Bonjour {$data['name']},</p>
                <p>Nous avons bien reçu votre message et nous vous remercions de votre intérêt pour nos services.</p>
                <p>Un de nos experts vous contactera dans les plus brefs délais pour discuter de votre projet.</p>
                <div class='cta'>
                    <p>En attendant, n'hésitez pas à nous appeler au <a href='tel:+33123456789'>+33 1 23 45 67 89</a></p>
                </div>
                <p>Cordialement,<br>L'équipe CitizenshipPro</p>
            </div>
        </div>
    </body>
    </html>";
}

function generateConfirmationEmailEN($data) {
    return "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <title>Thank you for your message</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #1a365d; color: white; padding: 20px; text-align: center; }
            .content { background: #f9f9f9; padding: 20px; }
            .cta { background: #d4af37; color: white; padding: 15px; text-align: center; margin: 20px 0; border-radius: 5px; }
            .cta a { color: white; text-decoration: none; font-weight: bold; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>Thank you for your message!</h1>
            </div>
            <div class='content'>
                <p>Hello {$data['name']},</p>
                <p>We have received your message and thank you for your interest in our services.</p>
                <p>One of our experts will contact you shortly to discuss your project.</p>
                <div class='cta'>
                    <p>In the meantime, feel free to call us at <a href='tel:+33123456789'>+33 1 23 45 67 89</a></p>
                </div>
                <p>Best regards,<br>The CitizenshipPro Team</p>
            </div>
        </div>
    </body>
    </html>";
}

/**
 * Basic spam detection
 */
function isSpam($data) {
    $spamKeywords = ['viagra', 'casino', 'lottery', 'winner', 'congratulations', 'urgent', 'click here'];
    $message = strtolower($data['message']);
    
    foreach ($spamKeywords as $keyword) {
        if (strpos($message, $keyword) !== false) {
            return true;
        }
    }
    
    // Check for too many links
    if (substr_count($message, 'http') > 2) {
        return true;
    }
    
    return false;
}

/**
 * Log form submission
 */
function logSubmission($data) {
    $logFile = __DIR__ . '/logs/submissions.log';
    $logDir = dirname($logFile);
    
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $logEntry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'name' => $data['name'],
        'email' => $data['email'],
        'type' => $data['type'],
        'ip' => $data['ip'],
        'user_agent' => $data['user_agent']
    ];
    
    file_put_contents($logFile, json_encode($logEntry) . "\n", FILE_APPEND | LOCK_EX);
}

/**
 * Sanitize input data
 */
function sanitizeInput($input, $type = 'string') {
    switch ($type) {
        case 'string':
            return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
        case 'email':
            return filter_var(trim($input), FILTER_SANITIZE_EMAIL);
        case 'phone':
            return preg_replace('/[^0-9+\-\s\(\)]/', '', trim($input));
        case 'alphanumeric':
            return preg_replace('/[^a-zA-Z0-9_-]/', '', trim($input));
        default:
            return trim($input);
    }
}
?>
