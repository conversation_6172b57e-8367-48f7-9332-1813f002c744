{"name": "citizenshippro/website", "description": "Professional multilingual website for citizenship and residency by investment programs", "type": "project", "keywords": ["citizenship", "residency", "investment", "immigration", "multilingual"], "license": "MIT", "authors": [{"name": "CitizenshipPro Team", "email": "<EMAIL>"}], "require": {"php": ">=7.4", "phpmailer/phpmailer": "^6.8"}, "require-dev": {"phpunit/phpunit": "^9.0"}, "autoload": {"psr-4": {"CitizenshipPro\\": "src/"}}, "autoload-dev": {"psr-4": {"CitizenshipPro\\Tests\\": "tests/"}}, "scripts": {"test": "phpunit", "clear-cache": "php -r \"array_map('unlink', glob('backend/lang/cache/*.json'));\"", "setup": ["@composer install", "php -r \"if (!is_dir('backend/lang/cache')) mkdir('backend/lang/cache', 0755, true);\"", "php -r \"if (!is_dir('backend/mail/logs')) mkdir('backend/mail/logs', 0755, true);\"", "echo 'Setup complete! Please configure SMTP settings in backend/mail/smtp.php'"]}, "config": {"optimize-autoloader": true, "sort-packages": true}, "minimum-stability": "stable", "prefer-stable": true}