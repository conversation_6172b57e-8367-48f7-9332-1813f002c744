# CitizenshipPro - Site Web Multilingue

Site web professionnel et multilingue pour une entreprise spécialisée dans les programmes de citoyenneté et résidence par investissement.

## 🚀 Fonctionnalités

- ✨ **SPA responsive** (mobile-first) avec navigation fluide sans rechargement
- 🌍 **Multilingue** (français & anglais) avec contenu dynamique
- 📩 **Formulaire de contact** avec PHPMailer et page de remerciement
- ⚡️ **Routing client** history-based incluant le code langue
- 📦 **Contenu dynamique** injecté selon la route
- 🎨 **Design haut de gamme** inspiré des leaders du secteur
- 📱 **Mobile-first** et entièrement responsive
- 🔧 **Architecture modulaire** avec JavaScript Vanilla et Lit Library

## 🏗️ Architecture Technique

### Frontend
- **HTML5** - Structure sémantique
- **CSS3** - Design system avec variables CSS
- **JavaScript Vanilla** - Logique applicative
- **Lit Library** - Composants pour le rendu HTML
- **Router client** - Navigation SPA avec support multilingue

### Backend
- **PHP** - API pour le contenu multilingue
- **PHPMailer** - Système d'envoi d'emails
- **Cache JSON** - Optimisation des performances

## 📁 Structure du Projet

```
├── public/
│   ├── index.html                ← Entrée principale (1 seule page HTML)
│   └── assets/
│       ├── css/
│       │   └── style.css         ← Styles principaux
│       ├── js/
│       │   ├── main.js           ← Initialise routeur & langue
│       │   ├── Router.js         ← Router history-based
│       │   ├── modules/          ← Logique des pages
│       │   │   ├── home.js
│       │   │   ├── contact.js
│       │   │   ├── 404.js
│       │   │   └── thank-you.js
│       │   └── templates/        ← Composants Lit pour le rendu HTML
│       │       ├── home-template.js
│       │       ├── contact-template.js
│       │       ├── 404-template.js
│       │       └── thank-you-template.js
│       └── img/                  ← Images et assets
│
├── backend/
│   ├── lang/
│   │   ├── index.php             ← API multilingue
│   │   ├── fr.php                ← Contenu français
│   │   ├── en.php                ← Contenu anglais
│   │   └── cache/                ← Cache JSON
│   └── mail/
│       ├── mailer.php            ← Envoi des formulaires
│       └── smtp.php              ← Configuration SMTP
│
└── vendor/                       ← Dépendances (PHPMailer)
```

## 🎯 Pages Implémentées

### ✅ Pages Complètes
- **Accueil (/)** - Hero, stats, pays populaires, fonctionnalités
- **Contact (/contact)** - Formulaire complet avec validation
- **Merci (/thank-you)** - Page de confirmation après envoi
- **404 (/404)** - Page d'erreur avec suggestions

### 🚧 Pages à Développer
- **Programmes de Citoyenneté (/citizenship)**
- **Programmes de Résidence (/residency)**
- **Services (/services)**
- **À propos (/about)**
- **Blog (/blog)**
- **Fiches pays (/citizenship/:country, /residency/:country)**

## 🛠️ Installation et Configuration

### 1. Serveur Web
Configurez votre serveur web (Apache/Nginx) pour pointer vers le dossier `public/`.

### 2. Configuration Email
Éditez `backend/mail/smtp.php` avec vos paramètres SMTP :

```php
$smtpConfig = [
    'host' => 'smtp.gmail.com',
    'port' => 587,
    'username' => '<EMAIL>',
    'password' => 'votre-mot-de-passe-app',
    'from_email' => '<EMAIL>',
    'admin_email' => '<EMAIL>',
];
```

### 3. PHPMailer
Installez PHPMailer via Composer :

```bash
composer require phpmailer/phpmailer
```

### 4. Permissions
Assurez-vous que les dossiers suivants sont accessibles en écriture :
- `backend/lang/cache/`
- `backend/mail/logs/`

## 🌐 Système Multilingue

### URLs Supportées
- **Français (défaut)** : `/`, `/contact`, `/services`
- **Anglais** : `/en`, `/en/contact`, `/en/services`

### Ajout d'une Langue
1. Créer `backend/lang/[code].php` avec les traductions
2. Ajouter le code langue dans `Router.js` (`supportedLangs`)
3. Mettre à jour la navigation dans `index.html`

## 📧 Système de Contact

### Fonctionnalités
- ✅ Validation côté client et serveur
- ✅ Protection anti-spam basique
- ✅ Email de confirmation automatique
- ✅ Logging des soumissions
- ✅ Support multilingue

### Champs du Formulaire
- Nom complet (obligatoire)
- Email (obligatoire)
- Téléphone (optionnel)
- Pays de résidence
- Programme d'intérêt
- Message (obligatoire)

## 🎨 Design System

### Couleurs Principales
- **Primary** : `#1a365d` (Bleu marine)
- **Secondary** : `#d4af37` (Or)
- **Accent** : `#e53e3e` (Rouge)
- **Success** : `#38a169` (Vert)

### Typographie
- **Headings** : Playfair Display (serif)
- **Body** : Inter (sans-serif)

### Breakpoints
- **Mobile** : < 768px
- **Tablet** : 768px - 1024px
- **Desktop** : > 1024px

## 🔧 Développement

### Ajout d'une Nouvelle Page

1. **Créer le module** dans `public/assets/js/modules/`
2. **Créer le template** dans `public/assets/js/templates/`
3. **Ajouter la route** dans `Router.js`
4. **Ajouter les traductions** dans `backend/lang/fr.php` et `en.php`
5. **Mettre à jour la navigation** dans `index.html`

### Structure d'un Module
```javascript
class MonModule {
    constructor() {
        this.data = null;
        this.lang = 'fr';
    }

    async init(context) {
        this.lang = context.lang;
        await this.loadContent();
        this.setupEventListeners();
    }

    async loadContent() {
        // Charger le contenu depuis l'API
    }

    setupEventListeners() {
        // Configurer les événements
    }
}

export default new MonModule();
```

## 📊 Analytics et Tracking

Le site inclut des hooks pour :
- Google Analytics (gtag)
- Facebook Pixel (fbq)
- Tracking personnalisé

## 🚀 Déploiement

### Prérequis
- PHP 7.4+
- Serveur web (Apache/Nginx)
- Composer
- Accès SMTP pour l'envoi d'emails

### Étapes
1. Cloner le repository
2. Configurer le serveur web
3. Installer les dépendances : `composer install`
4. Configurer SMTP dans `backend/mail/smtp.php`
5. Ajuster les permissions des dossiers
6. Tester le formulaire de contact

## 🔒 Sécurité

### Mesures Implémentées
- Validation et sanitisation des entrées
- Protection CSRF basique
- Limitation des tentatives de spam
- Headers de sécurité CORS

### Recommandations
- Utiliser HTTPS en production
- Configurer un firewall web
- Mettre à jour régulièrement PHP et les dépendances
- Monitorer les logs d'erreur

## 📝 TODO

### Priorité Haute
- [ ] Compléter les pages manquantes (citizenship, residency, services, about, blog)
- [ ] Ajouter plus de contenu et d'images
- [ ] Optimiser les performances (lazy loading, compression)
- [ ] Tests cross-browser

### Priorité Moyenne
- [ ] Système de cache plus avancé
- [ ] Optimisation SEO (meta tags, structured data)
- [ ] Intégration CMS pour le blog
- [ ] Système de réservation de consultation

### Priorité Basse
- [ ] Mode sombre
- [ ] Animations avancées
- [ ] PWA (Progressive Web App)
- [ ] Intégration avec CRM

## 📞 Support

Pour toute question ou assistance, contactez l'équipe de développement.

---

**CitizenshipPro** - Votre partenaire de confiance pour l'immigration par investissement.
