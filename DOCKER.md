# CitizenshipPro - Guide Docker

Ce guide vous explique comment utiliser Docker pour développer et déployer l'application CitizenshipPro.

## 🚀 Démarrage Rapide

### Méthode 1: Script automatisé (Recommandé)

```bash
# Démarrer l'application
./docker-start.sh start

# Voir les logs
./docker-start.sh logs

# Arrêter l'application
./docker-start.sh stop
```

### Méthode 2: Commandes Docker manuelles

```bash
# Construire l'image
docker build -t citizenshippro:latest .

# Lancer le conteneur
docker run -d --name citizenshippro-web -p 8080:80 citizenshippro:latest

# Voir les logs
docker logs -f citizenshippro-web

# Arrêter le conteneur
docker stop citizenshippro-web && docker rm citizenshippro-web
```

### Méthode 3: Docker Compose

```bash
# Démarrer tous les services
docker-compose up -d

# Voir les logs
docker-compose logs -f web

# Arrêter tous les services
docker-compose down
```

## 🏗️ Architecture Docker

### Image de Base
- **PHP 8.2** avec Apache
- **Multi-stage build** pour optimiser la taille
- **Extensions PHP** : PDO, mbstring, GD, etc.
- **Composer** pour la gestion des dépendances

### Configuration Apache
- **Document Root** : `/var/www/html/public`
- **Modules activés** : rewrite, headers, deflate, expires
- **Headers de sécurité** configurés
- **Compression** activée
- **Cache navigateur** optimisé

### Volumes Persistants
- `backend/lang/cache` - Cache des traductions
- `backend/mail/logs` - Logs des emails

## 📋 Services Disponibles

### Service Principal (web)
- **Port** : 8080
- **URL** : http://localhost:8080
- **Health Check** : Vérifie que l'application répond

### Services de Développement

#### Base de Données MySQL
- **Port** : 3306
- **Utilisateur** : citizenshippro_user
- **Mot de passe** : citizenshippro_pass_2024
- **Base** : citizenshippro

#### phpMyAdmin
- **Port** : 8081
- **URL** : http://localhost:8081
- **Profile** : development

#### Mailhog (Test d'emails)
- **SMTP Port** : 1025
- **Web UI Port** : 8025
- **URL** : http://localhost:8025
- **Profile** : development

#### Redis Cache
- **Port** : 6379
- **Profile** : cache

### Démarrer avec des profiles spécifiques

```bash
# Développement complet (avec phpMyAdmin et Mailhog)
docker-compose --profile development up -d

# Avec cache Redis
docker-compose --profile cache up -d

# Production (avec Nginx)
docker-compose --profile production up -d
```

## 🔧 Configuration

### Variables d'Environnement

```bash
# Dans docker-compose.yml ou .env
APACHE_DOCUMENT_ROOT=/var/www/html/public
PHP_MEMORY_LIMIT=256M
PHP_MAX_EXECUTION_TIME=300
PHP_UPLOAD_MAX_FILESIZE=50M
PHP_POST_MAX_SIZE=50M
```

### Configuration SMTP pour Mailhog (Développement)

```php
// backend/mail/smtp.php
$smtpConfig = [
    'host' => 'mailhog',  // Nom du service Docker
    'port' => 1025,
    'encryption' => '',   // Pas de chiffrement pour Mailhog
    'auth' => false,      // Pas d'authentification
    // ...
];
```

## 🛠️ Développement

### Mode Développement avec Volumes

```bash
# Monter le code source pour le développement
docker run -d \
  --name citizenshippro-dev \
  -p 8080:80 \
  -v $(pwd)/public:/var/www/html/public \
  -v $(pwd)/backend:/var/www/html/backend \
  citizenshippro:latest
```

### Debugging

```bash
# Accéder au conteneur
docker exec -it citizenshippro-web bash

# Voir les logs Apache
docker exec citizenshippro-web tail -f /var/log/apache2/error.log

# Voir les logs PHP
docker exec citizenshippro-web tail -f /var/log/php_errors.log
```

### Tests

```bash
# Tester l'API
curl http://localhost:8080/backend/lang/index.php?lang=fr&page=home

# Tester les headers de sécurité
curl -I http://localhost:8080

# Health check
curl http://localhost:8080/
```

## 🚀 Production

### Build Optimisé

```bash
# Build pour production
docker build --target production -t citizenshippro:prod .

# Ou avec Docker Compose
docker-compose -f docker-compose.prod.yml build
```

### Configuration Production

1. **Variables d'environnement** :
   ```bash
   # .env.production
   SMTP_HOST=your-smtp-server.com
   SMTP_USERNAME=<EMAIL>
   SMTP_PASSWORD=your-password
   ```

2. **SSL/TLS** :
   - Placer les certificats dans `docker/ssl/`
   - Configurer Nginx pour HTTPS

3. **Monitoring** :
   ```bash
   # Voir l'état des conteneurs
   docker-compose ps
   
   # Voir l'utilisation des ressources
   docker stats
   ```

## 📊 Monitoring et Logs

### Logs Centralisés

```bash
# Tous les logs
docker-compose logs

# Logs d'un service spécifique
docker-compose logs web

# Suivre les logs en temps réel
docker-compose logs -f web
```

### Health Checks

```bash
# Vérifier la santé du conteneur
docker inspect citizenshippro-web | grep Health -A 10

# Tester manuellement
curl -f http://localhost:8080/ || echo "Service down"
```

### Métriques

```bash
# Utilisation des ressources
docker stats citizenshippro-web

# Espace disque
docker system df

# Nettoyer les ressources inutilisées
docker system prune
```

## 🔒 Sécurité

### Bonnes Pratiques Implémentées

1. **Utilisateur non-root** dans le conteneur
2. **Headers de sécurité** configurés
3. **Fichiers sensibles** exclus (.dockerignore)
4. **Permissions** appropriées sur les dossiers
5. **Multi-stage build** pour réduire la surface d'attaque

### Recommandations Production

1. **Secrets** : Utiliser Docker Secrets ou variables d'environnement
2. **Network** : Isoler les services avec des réseaux Docker
3. **Updates** : Maintenir les images à jour
4. **Scanning** : Scanner les vulnérabilités avec `docker scan`

## 🐛 Dépannage

### Problèmes Courants

#### Le conteneur ne démarre pas
```bash
# Voir les logs de démarrage
docker logs citizenshippro-web

# Vérifier la configuration
docker inspect citizenshippro-web
```

#### L'application ne répond pas
```bash
# Vérifier que le port est bien exposé
docker port citizenshippro-web

# Tester depuis l'intérieur du conteneur
docker exec citizenshippro-web curl localhost
```

#### Problèmes de permissions
```bash
# Vérifier les permissions des volumes
docker exec citizenshippro-web ls -la /var/www/html/backend/lang/cache

# Corriger les permissions
docker exec citizenshippro-web chown -R www-data:www-data /var/www/html/backend/lang/cache
```

#### Problèmes SMTP
```bash
# Tester la connexion SMTP depuis le conteneur
docker exec citizenshippro-web php -r "
require '/var/www/html/backend/mail/smtp.php';
var_dump(testSMTPConnection());
"
```

### Nettoyage

```bash
# Nettoyer tout
./docker-start.sh cleanup

# Ou manuellement
docker stop $(docker ps -aq)
docker rm $(docker ps -aq)
docker rmi citizenshippro:latest
docker system prune -f
```

## 📚 Ressources

- [Documentation Docker](https://docs.docker.com/)
- [Docker Compose Reference](https://docs.docker.com/compose/)
- [PHP Docker Images](https://hub.docker.com/_/php)
- [Apache Configuration](https://httpd.apache.org/docs/)

## 🆘 Support

Si vous rencontrez des problèmes :

1. Vérifiez les logs : `./docker-start.sh logs`
2. Testez l'état : `./docker-start.sh status`
3. Consultez ce guide de dépannage
4. Ouvrez une issue sur le repository Git
