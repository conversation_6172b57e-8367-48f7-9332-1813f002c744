version: '3.8'

services:
  # CitizenshipPro Web Application
  web:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: citizenshippro-web
    ports:
      - "8080:80"
    volumes:
      # Development volumes (comment out for production)
      - ./public:/var/www/html/public
      - ./backend:/var/www/html/backend
      - ./backend/lang/cache:/var/www/html/backend/lang/cache
      - ./backend/mail/logs:/var/www/html/backend/mail/logs
    environment:
      - APACHE_DOCUMENT_ROOT=/var/www/html/public
      - PHP_MEMORY_LIMIT=256M
      - PHP_MAX_EXECUTION_TIME=300
      - PHP_UPLOAD_MAX_FILESIZE=50M
      - PHP_POST_MAX_SIZE=50M
    networks:
      - citizenshippro-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MySQL Database (optional - for future use)
  database:
    image: mysql:8.0
    container_name: citizenshippro-db
    environment:
      MYSQL_ROOT_PASSWORD: citizenshippro_root_2024
      MYSQL_DATABASE: citizenshippro
      MYSQL_USER: citizenshippro_user
      MYSQL_PASSWORD: citizenshippro_pass_2024
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    networks:
      - citizenshippro-network
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password

  # phpMyAdmin (development only)
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: citizenshippro-phpmyadmin
    environment:
      PMA_HOST: database
      PMA_USER: citizenshippro_user
      PMA_PASSWORD: citizenshippro_pass_2024
      MYSQL_ROOT_PASSWORD: citizenshippro_root_2024
    ports:
      - "8081:80"
    networks:
      - citizenshippro-network
    depends_on:
      - database
    restart: unless-stopped
    profiles:
      - development

  # Redis Cache (optional - for future use)
  redis:
    image: redis:7-alpine
    container_name: citizenshippro-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - citizenshippro-network
    restart: unless-stopped
    command: redis-server --appendonly yes
    profiles:
      - cache

  # Mailhog (development email testing)
  mailhog:
    image: mailhog/mailhog:latest
    container_name: citizenshippro-mailhog
    ports:
      - "1025:1025"  # SMTP port
      - "8025:8025"  # Web UI port
    networks:
      - citizenshippro-network
    restart: unless-stopped
    profiles:
      - development

  # Nginx (production reverse proxy)
  nginx:
    image: nginx:alpine
    container_name: citizenshippro-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/sites:/etc/nginx/conf.d
      - ./docker/ssl:/etc/nginx/ssl
    networks:
      - citizenshippro-network
    depends_on:
      - web
    restart: unless-stopped
    profiles:
      - production

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  citizenshippro-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
