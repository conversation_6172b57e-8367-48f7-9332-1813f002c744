#!/bin/bash

# CitizenshipPro Docker Startup Script
# This script helps you build and run the CitizenshipPro application with Docker

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
IMAGE_NAME="citizenshippro"
CONTAINER_NAME="citizenshippro-web"
PORT="8080"
DOCKER_COMPOSE_FILE="docker-compose.yml"

# Functions
print_header() {
    echo -e "${BLUE}"
    echo "🚀 CitizenshipPro Docker Manager"
    echo "================================"
    echo -e "${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    print_success "Docker is available and running"
}

check_docker_compose() {
    if ! command -v docker-compose &> /dev/null; then
        print_warning "Docker Compose is not installed. Using docker commands instead."
        return 1
    fi
    return 0
}

build_image() {
    print_info "Building Docker image..."
    
    if docker build -t ${IMAGE_NAME}:latest .; then
        print_success "Docker image built successfully"
    else
        print_error "Failed to build Docker image"
        exit 1
    fi
}

stop_existing_container() {
    if docker ps -q -f name=${CONTAINER_NAME} | grep -q .; then
        print_info "Stopping existing container..."
        docker stop ${CONTAINER_NAME}
        docker rm ${CONTAINER_NAME}
        print_success "Existing container stopped and removed"
    fi
}

run_container() {
    print_info "Starting new container..."
    
    if docker run -d \
        --name ${CONTAINER_NAME} \
        -p ${PORT}:80 \
        -v "$(pwd)/backend/lang/cache:/var/www/html/backend/lang/cache" \
        -v "$(pwd)/backend/mail/logs:/var/www/html/backend/mail/logs" \
        ${IMAGE_NAME}:latest; then
        print_success "Container started successfully"
        print_info "Application is available at: http://localhost:${PORT}"
    else
        print_error "Failed to start container"
        exit 1
    fi
}

run_with_compose() {
    print_info "Starting with Docker Compose..."
    
    if docker-compose up -d web; then
        print_success "Services started with Docker Compose"
        print_info "Application is available at: http://localhost:${PORT}"
        print_info "Use 'docker-compose logs -f web' to view logs"
    else
        print_error "Failed to start with Docker Compose"
        exit 1
    fi
}

show_status() {
    print_info "Container status:"
    docker ps -f name=${CONTAINER_NAME}
    
    echo ""
    print_info "Application health check:"
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:${PORT} | grep -q "200"; then
        print_success "Application is responding correctly"
    else
        print_warning "Application may not be ready yet. Wait a moment and try again."
    fi
}

show_logs() {
    print_info "Showing container logs (press Ctrl+C to exit):"
    docker logs -f ${CONTAINER_NAME}
}

stop_services() {
    print_info "Stopping services..."
    
    if check_docker_compose && [ -f ${DOCKER_COMPOSE_FILE} ]; then
        docker-compose down
    else
        if docker ps -q -f name=${CONTAINER_NAME} | grep -q .; then
            docker stop ${CONTAINER_NAME}
            docker rm ${CONTAINER_NAME}
        fi
    fi
    
    print_success "Services stopped"
}

cleanup() {
    print_info "Cleaning up Docker resources..."
    
    # Stop and remove containers
    docker ps -aq -f name=${CONTAINER_NAME} | xargs -r docker rm -f
    
    # Remove images (optional)
    read -p "Do you want to remove the Docker image as well? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker rmi ${IMAGE_NAME}:latest 2>/dev/null || true
        print_success "Docker image removed"
    fi
    
    print_success "Cleanup completed"
}

show_help() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  build     Build the Docker image"
    echo "  start     Build and start the application"
    echo "  stop      Stop the application"
    echo "  restart   Restart the application"
    echo "  status    Show application status"
    echo "  logs      Show application logs"
    echo "  cleanup   Clean up Docker resources"
    echo "  compose   Start with Docker Compose"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start    # Build and start the application"
    echo "  $0 logs     # View application logs"
    echo "  $0 stop     # Stop the application"
}

# Main script
print_header

case "${1:-start}" in
    "build")
        check_docker
        build_image
        ;;
    "start")
        check_docker
        build_image
        stop_existing_container
        run_container
        sleep 3
        show_status
        ;;
    "stop")
        check_docker
        stop_services
        ;;
    "restart")
        check_docker
        stop_services
        build_image
        run_container
        sleep 3
        show_status
        ;;
    "status")
        check_docker
        show_status
        ;;
    "logs")
        check_docker
        show_logs
        ;;
    "cleanup")
        check_docker
        cleanup
        ;;
    "compose")
        check_docker
        if check_docker_compose; then
            run_with_compose
        else
            print_error "Docker Compose is required for this command"
            exit 1
        fi
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac
